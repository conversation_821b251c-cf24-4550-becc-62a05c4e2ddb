{"name": "xcode-mcp-server", "version": "1.0.3", "description": "An MCP server for Xcode integration, enabling AI assistants to interact with Xcode projects", "type": "module", "bin": {"xcode-server": "./dist/index.js"}, "files": ["dist", "README.md", "LICENSE"], "scripts": {"clean": "rm -rf dist", "prebuild": "npm run clean", "build": "tsc && chmod +x dist/index.js", "build:production": "npm run clean && tsc --sourceMap false --declaration false && chmod +x dist/index.js", "build:verify": "npm run build && npm run verify", "prepare": "npm run build", "watch": "tsc --watch", "start": "node dist/index.js", "dev": "tsc && node dist/index.js", "debug": "DEBUG=true node --trace-warnings dist/index.js", "inspector": "npx @modelcontextprotocol/inspector dist/index.js", "verify": "npm run audit:security && echo '✅ Build verification completed successfully'", "audit:security": "npm audit --audit-level=moderate", "audit:fix": "npm audit fix"}, "keywords": ["xcode", "mcp", "ai", "llm", "ios", "development", "claude", "modelcontextprotocol"], "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/r-huijts/xcode-mcp-server.git"}, "bugs": {"url": "https://github.com/r-huijts/xcode-mcp-server/issues"}, "homepage": "https://github.com/r-huijts/xcode-mcp-server#readme", "engines": {"node": ">=20.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.9.0", "glob": "^11.0.1", "zod": "^3.24.2", "dotenv": "^16.4.5"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/node": "^20.17.28", "typescript": "^5.8.2"}}
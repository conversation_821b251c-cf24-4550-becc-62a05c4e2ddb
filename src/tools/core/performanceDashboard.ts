import { z } from "zod";
import { ToolBase, ToolResult } from "../../utils/toolInfrastructure.js";
import { XcodeServer } from "../../server.js";
import { ToolCategory, ToolRegistry } from "../categories.js";
import { globalPerformanceMonitor } from "../../utils/performanceMonitor.js";
import { CacheManager } from "../../utils/cacheManager.js";

/**
 * Performance monitoring dashboard tool
 */
export class PerformanceDashboardTool extends ToolBase<{
  timeRange?: string;
  includeRegressions?: boolean;
  includeTrends?: boolean;
  includeCache?: boolean;
  format?: "summary" | "detailed" | "json";
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "performance_dashboard",
      "Comprehensive performance monitoring dashboard with metrics, trends, and regression analysis",
      z.object({
        timeRange: z
          .enum(["1h", "6h", "24h", "7d", "30d"])
          .optional()
          .describe("Time range for metrics"),
        includeRegressions: z
          .boolean()
          .optional()
          .describe("Include regression analysis"),
        includeTrends: z
          .boolean()
          .optional()
          .describe("Include trend analysis"),
        includeCache: z
          .boolean()
          .optional()
          .describe("Include cache statistics"),
        format: z
          .enum(["summary", "detailed", "json"])
          .optional()
          .describe("Output format"),
      })
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.DEVELOPMENT,
      description: this.description,
      tags: ["performance", "monitoring", "dashboard", "analytics"],
      complexity: "advanced",
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "2.0.0",
    });
  }

  protected async executeImpl(params: {
    timeRange?: string;
    includeRegressions?: boolean;
    includeTrends?: boolean;
    includeCache?: boolean;
    format?: "summary" | "detailed" | "json";
  }): Promise<ToolResult> {
    try {
      const timeRange = params.timeRange || "24h";
      const format = params.format || "summary";

      // Collect performance data
      const performanceData = this.collectPerformanceData(timeRange);

      // Collect regression data if requested
      let regressionData;
      if (params.includeRegressions !== false) {
        regressionData = await this.collectRegressionData(timeRange);
      }

      // Collect trend data if requested
      let trendData;
      if (params.includeTrends !== false) {
        trendData = await this.collectTrendData();
      }

      // Collect cache data if requested
      let cacheData;
      if (params.includeCache !== false) {
        cacheData = this.collectCacheData();
      }

      // Generate dashboard based on format
      const dashboard = this.generateDashboard({
        performance: performanceData,
        regressions: regressionData,
        trends: trendData,
        cache: cacheData,
        timeRange,
        format,
      });

      return this.createSuccessResponse(
        "Performance dashboard generated successfully",
        dashboard
      );
    } catch (error) {
      return this.createErrorResponse(
        "Failed to generate performance dashboard",
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  private collectPerformanceData(timeRange: string): any {
    const operationNames = globalPerformanceMonitor.getOperationNames();
    const timeRangeMs = this.parseTimeRange(timeRange);
    const cutoffTime = Date.now() - timeRangeMs;

    const operations = operationNames
      .map((name) => {
        const stats = globalPerformanceMonitor.getStats(name);
        if (!stats) return null;

        // Filter recent metrics
        const recentMetrics = stats.recentMetrics.filter(
          (metric) => metric.timestamp.getTime() > cutoffTime
        );

        if (recentMetrics.length === 0) return null;

        const recentDurations = recentMetrics.map((m) => m.duration);
        const avgDuration =
          recentDurations.reduce((a, b) => a + b, 0) / recentDurations.length;
        const minDuration = Math.min(...recentDurations);
        const maxDuration = Math.max(...recentDurations);

        return {
          name,
          totalCalls: recentMetrics.length,
          averageDuration: Math.round(avgDuration),
          minDuration: Math.round(minDuration),
          maxDuration: Math.round(maxDuration),
          successRate:
            (recentMetrics.filter((m) => m.metadata?.success !== false).length /
              recentMetrics.length) *
            100,
          lastExecuted: recentMetrics[recentMetrics.length - 1]?.timestamp,
        };
      })
      .filter(Boolean);

    // Sort by total calls
    operations.sort((a, b) => (b?.totalCalls || 0) - (a?.totalCalls || 0));

    return {
      timeRange,
      totalOperations: operations.length,
      operations: operations.slice(0, 20), // Top 20
      summary: {
        totalCalls: operations.reduce(
          (sum, op) => sum + (op?.totalCalls || 0),
          0
        ),
        averageResponseTime: Math.round(
          operations.reduce((sum, op) => sum + (op?.averageDuration || 0), 0) /
            operations.length
        ),
        overallSuccessRate: Math.round(
          operations.reduce((sum, op) => sum + (op?.successRate || 0), 0) /
            operations.length
        ),
      },
    };
  }

  private async collectRegressionData(timeRange: string): Promise<any> {
    const timeRangeMs = this.parseTimeRange(timeRange);
    const since = new Date(Date.now() - timeRangeMs);

    const regressions = globalPerformanceMonitor.getRegressionHistory(
      undefined,
      since
    );

    const severityCounts = {
      critical: regressions.filter((r) => r.severity === "critical").length,
      high: regressions.filter((r) => r.severity === "high").length,
      medium: regressions.filter((r) => r.severity === "medium").length,
      low: regressions.filter((r) => r.severity === "low").length,
    };

    const topRegressions = regressions
      .sort((a, b) => b.regressionPercentage - a.regressionPercentage)
      .slice(0, 10);

    return {
      totalRegressions: regressions.length,
      severityCounts,
      topRegressions: topRegressions.map((r) => ({
        operation: r.operationName,
        severity: r.severity,
        regressionPercentage: Math.round(r.regressionPercentage * 100) / 100,
        currentDuration: Math.round(r.currentDuration),
        baselineDuration: Math.round(r.baselineDuration),
        timestamp: r.timestamp,
      })),
      recommendations: this.generateRegressionRecommendations(regressions),
    };
  }

  private async collectTrendData(): Promise<any> {
    const trends = globalPerformanceMonitor.getCurrentTrends();

    const trendCounts = {
      improving: trends.filter((t) => t.trend === "improving").length,
      stable: trends.filter((t) => t.trend === "stable").length,
      degrading: trends.filter((t) => t.trend === "degrading").length,
    };

    const significantTrends = trends
      .filter((t) => Math.abs(t.changePercentage) > 5)
      .sort(
        (a, b) => Math.abs(b.changePercentage) - Math.abs(a.changePercentage)
      )
      .slice(0, 10);

    return {
      totalTrends: trends.length,
      trendCounts,
      significantTrends: significantTrends.map((t) => ({
        operation: t.operationName,
        trend: t.trend,
        changePercentage: Math.round(t.changePercentage * 100) / 100,
        dataPoints: t.dataPoints,
        timespan: this.formatDuration(t.timespan),
      })),
    };
  }

  private collectCacheData(): any {
    const globalStats = CacheManager.getGlobalStatistics();

    return {
      totalCaches: globalStats.cacheCount,
      globalStats: {
        totalHits: globalStats.totalHits,
        totalMisses: globalStats.totalMisses,
        totalEvictions: globalStats.totalEvictions,
        hitRate:
          globalStats.totalHits + globalStats.totalMisses > 0
            ? Math.round(
                (globalStats.totalHits /
                  (globalStats.totalHits + globalStats.totalMisses)) *
                  100
              )
            : 0,
        memoryUsage: this.formatBytes(globalStats.totalMemoryUsage),
      },
      individualCaches: Object.entries(globalStats.individualCaches).map(
        ([name, stats]) => ({
          name,
          hits: stats.hits,
          misses: stats.misses,
          hitRate: Math.round(stats.hitRate * 100),
          size: stats.entries,
          memoryUsage: this.formatBytes(stats.memoryUsage),
        })
      ),
    };
  }

  private generateDashboard(data: {
    performance: any;
    regressions?: any;
    trends?: any;
    cache?: any;
    timeRange: string;
    format: string;
  }): any {
    if (data.format === "json") {
      return data;
    }

    if (data.format === "summary") {
      return this.generateSummaryDashboard(data);
    }

    return this.generateDetailedDashboard(data);
  }

  private generateSummaryDashboard(data: any): any {
    const summary: any = {
      timeRange: data.timeRange,
      timestamp: new Date().toISOString(),
      overview: {
        totalOperations: data.performance.totalOperations,
        totalCalls: data.performance.summary.totalCalls,
        averageResponseTime: `${data.performance.summary.averageResponseTime}ms`,
        successRate: `${data.performance.summary.overallSuccessRate}%`,
      },
    };

    if (data.regressions) {
      summary.overview.regressions = {
        total: data.regressions.totalRegressions,
        critical: data.regressions.severityCounts.critical,
        high: data.regressions.severityCounts.high,
      };
    }

    if (data.cache) {
      summary.overview.cache = {
        hitRate: `${data.cache.globalStats.hitRate}%`,
        memoryUsage: data.cache.globalStats.memoryUsage,
      };
    }

    return summary;
  }

  private generateDetailedDashboard(data: any): any {
    return {
      timeRange: data.timeRange,
      timestamp: new Date().toISOString(),
      performance: data.performance,
      regressions: data.regressions,
      trends: data.trends,
      cache: data.cache,
      healthScore: this.calculateHealthScore(data),
      recommendations: this.generateRecommendations(data),
    };
  }

  private calculateHealthScore(data: any): number {
    let score = 100;

    // Deduct for regressions
    if (data.regressions) {
      score -= data.regressions.severityCounts.critical * 20;
      score -= data.regressions.severityCounts.high * 10;
      score -= data.regressions.severityCounts.medium * 5;
      score -= data.regressions.severityCounts.low * 2;
    }

    // Deduct for poor success rate
    if (data.performance.summary.overallSuccessRate < 95) {
      score -= (95 - data.performance.summary.overallSuccessRate) * 2;
    }

    // Deduct for poor cache hit rate
    if (data.cache && data.cache.globalStats.hitRate < 70) {
      score -= (70 - data.cache.globalStats.hitRate) * 0.5;
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private generateRecommendations(data: any): string[] {
    const recommendations: string[] = [];

    if (data.regressions?.severityCounts.critical > 0) {
      recommendations.push(
        `🚨 ${data.regressions.severityCounts.critical} critical performance regressions require immediate attention`
      );
    }

    if (data.performance.summary.overallSuccessRate < 95) {
      recommendations.push(
        `⚠️ Success rate is ${data.performance.summary.overallSuccessRate}% - investigate failing operations`
      );
    }

    if (data.cache?.globalStats.hitRate < 70) {
      recommendations.push(
        `📈 Cache hit rate is ${data.cache.globalStats.hitRate}% - consider cache optimization`
      );
    }

    if (data.trends) {
      const degradingCount = data.trends.trendCounts.degrading;
      if (degradingCount > 5) {
        recommendations.push(
          `📉 ${degradingCount} operations showing degrading performance trends`
        );
      }
    }

    if (recommendations.length === 0) {
      recommendations.push(
        "✅ System performance is healthy - continue monitoring"
      );
    }

    return recommendations;
  }

  private generateRegressionRecommendations(regressions: any[]): string[] {
    const recommendations: string[] = [];

    const criticalOps = regressions
      .filter((r) => r.severity === "critical")
      .map((r) => r.operationName);

    if (criticalOps.length > 0) {
      recommendations.push(
        `Investigate critical regressions in: ${criticalOps.join(", ")}`
      );
    }

    return recommendations;
  }

  private parseTimeRange(timeRange: string): number {
    const ranges: Record<string, number> = {
      "1h": 3600000,
      "6h": 21600000,
      "24h": 86400000,
      "7d": 604800000,
      "30d": **********,
    };

    return ranges[timeRange] || ranges["24h"];
  }

  private formatDuration(ms: number): string {
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    if (ms < 3600000) return `${Math.round(ms / 60000)}m`;
    if (ms < 86400000) return `${Math.round(ms / 3600000)}h`;
    return `${Math.round(ms / 86400000)}d`;
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }
}

/**
 * Register performance dashboard tool
 */
export function registerPerformanceDashboard(server: XcodeServer): void {
  const dashboardTool = new PerformanceDashboardTool(server);
  dashboardTool.register();
}

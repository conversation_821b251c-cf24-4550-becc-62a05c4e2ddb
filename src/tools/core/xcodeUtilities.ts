import { z } from "zod";
import { <PERSON><PERSON>oolBase, ToolResult } from "../../utils/toolInfrastructure.js";
import { XcodeServer } from "../../server.js";
import { ToolCategory, ToolRegistry } from "../categories.js";

/**
 * Xcode information tool with comprehensive system analysis
 */
export class XcodeInfoTool extends CommandToolBase<{}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "get_xcode_info",
      "Get comprehensive Xcode installation information with system analysis",
      z.object({})
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.XCODE_UTILITIES,
      description: this.description,
      tags: ["xcode", "info", "system", "comprehensive"],
      complexity: "simple",
      requiresActiveProject: false,
      requiresXcode: true,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "2.0.0",
    });
  }

  protected getCommand(): string {
    return "xcode-select";
  }

  protected buildArgs(params: {}): string[] {
    return ["--print-path"];
  }

  protected async executeImpl(params: {}): Promise<ToolResult> {
    try {
      // Get Xcode path
      const xcodePathResult = await this.executeToolCommand(params);
      const xcodePath = xcodePathResult.stdout.trim();

      // Get Xcode version
      const versionResult = await this.executeCommand("xcodebuild", [
        "-version",
      ]);

      // Get available SDKs
      const sdksResult = await this.executeCommand("xcodebuild", ["-showsdks"]);

      // Get available simulators
      const simulatorsResult = await this.executeCommand("xcrun", [
        "simctl",
        "list",
        "runtimes",
        "--json",
      ]);

      // Parse simulator data
      let simulatorRuntimes = [];
      try {
        const simData = JSON.parse(simulatorsResult.stdout);
        simulatorRuntimes = simData.runtimes || [];
      } catch (error) {
        console.warn("Could not parse simulator data:", error);
      }

      // Get command line tools info
      let cltVersion = "Unknown";
      try {
        const cltResult = await this.executeCommand("pkgutil", [
          "--pkg-info=com.apple.pkg.CLTools_Executables",
        ]);
        const versionMatch = cltResult.stdout.match(/version: (.+)/);
        if (versionMatch) {
          cltVersion = versionMatch[1];
        }
      } catch (error) {
        // Command line tools might not be installed
      }

      return this.createSuccessResponse(
        "Successfully retrieved Xcode information",
        {
          xcodePath,
          version: versionResult.stdout.trim(),
          commandLineToolsVersion: cltVersion,
          availableSDKs: this.parseSDKs(sdksResult.stdout),
          simulatorRuntimes: simulatorRuntimes.map((runtime: any) => ({
            name: runtime.name,
            identifier: runtime.identifier,
            version: runtime.version,
            isAvailable: runtime.isAvailable,
          })),
          systemInfo: {
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version,
          },
        }
      );
    } catch (error) {
      return this.createErrorResponse(
        "Failed to retrieve Xcode information",
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  private parseSDKs(
    sdkOutput: string
  ): Array<{ name: string; version: string; platform: string }> {
    const sdks: Array<{ name: string; version: string; platform: string }> = [];
    const lines = sdkOutput.split("\n");

    for (const line of lines) {
      const match = line.match(/^([^-]+)-(.+?)\s+-sdk\s+(.+)$/);
      if (match) {
        sdks.push({
          platform: match[1].trim(),
          version: match[2].trim(),
          name: match[3].trim(),
        });
      }
    }

    return sdks;
  }
}

/**
 * Asset catalog compilation tool with optimization and validation
 */
export class AssetCatalogTool extends CommandToolBase<{
  catalogPath: string;
  outputDir: string;
  platform?: string;
  appIcon?: string;
  targetDevices?: string[];
  minDeploymentTarget?: string;
  optimization?: "space" | "time";
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "compile_asset_catalog",
      "Compile asset catalogs with optimization and validation",
      z.object({
        catalogPath: z.string().describe("Path to the .xcassets directory"),
        outputDir: z
          .string()
          .describe("Directory where compiled assets will be placed"),
        platform: z
          .enum(["iphoneos", "iphonesimulator", "macosx", "watchos"])
          .optional()
          .describe("Target platform"),
        appIcon: z
          .string()
          .optional()
          .describe("Name of the app icon set to include"),
        targetDevices: z
          .array(z.string())
          .optional()
          .describe("Target devices"),
        minDeploymentTarget: z
          .string()
          .optional()
          .describe("Minimum deployment target version"),
        optimization: z
          .enum(["space", "time"])
          .optional()
          .describe("Optimization strategy"),
      })
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.XCODE_UTILITIES,
      description: this.description,
      tags: ["assets", "compilation", "optimization"],
      complexity: "intermediate",
      requiresActiveProject: false,
      requiresXcode: true,
      platforms: ["ios", "macos", "watchos", "tvos"],
      version: "2.0.0",
    });
  }

  protected getCommand(): string {
    return "xcrun";
  }

  protected buildArgs(params: {
    catalogPath: string;
    outputDir: string;
    platform?: string;
    appIcon?: string;
    targetDevices?: string[];
    minDeploymentTarget?: string;
    optimization?: "space" | "time";
  }): string[] {
    const args = ["actool"];

    // Input catalog
    args.push(params.catalogPath);

    // Output directory
    args.push("--output-format", "human-readable-text");
    args.push("--notices");
    args.push("--warnings");
    args.push(
      "--export-dependency-info",
      `${params.outputDir}/assetcatalog_dependencies`
    );
    args.push(
      "--output-partial-info-plist",
      `${params.outputDir}/assetcatalog_generated_info.plist`
    );
    args.push("--app-icon", params.appIcon || "AppIcon");
    args.push("--compress-pngs");
    args.push("--enable-on-demand-resources", "YES");

    // Platform
    const platform = params.platform || "iphoneos";
    args.push("--platform", platform);

    // Target devices
    const targetDevices = params.targetDevices || ["iphone", "ipad"];
    for (const device of targetDevices) {
      args.push("--target-device", device);
    }

    // Deployment target
    const deploymentTarget = params.minDeploymentTarget || "14.0";
    args.push("--minimum-deployment-target", deploymentTarget);

    // Optimization
    if (params.optimization === "space") {
      args.push("--optimization", "space");
    } else if (params.optimization === "time") {
      args.push("--optimization", "time");
    }

    // Output directory
    args.push("--compile", params.outputDir);

    return args;
  }

  protected async executeImpl(params: {
    catalogPath: string;
    outputDir: string;
    platform?: string;
    appIcon?: string;
    targetDevices?: string[];
    minDeploymentTarget?: string;
    optimization?: "space" | "time";
  }): Promise<ToolResult> {
    try {
      // Validate input catalog exists
      const catalogPath = await this.validateAndResolvePath(
        params.catalogPath,
        "read"
      );

      // Ensure output directory exists
      const fs = await import("fs/promises");
      await fs.mkdir(params.outputDir, { recursive: true });

      // Compile assets
      const result = await this.executeToolCommand(params);

      // Parse compilation results
      const compilationInfo = this.parseCompilationOutput(result.stdout);

      return this.createSuccessResponse(
        `Successfully compiled asset catalog: ${params.catalogPath}`,
        {
          catalogPath,
          outputDir: params.outputDir,
          platform: params.platform || "iphoneos",
          compilationInfo,
          warnings: this.extractWarnings(result.stderr),
          optimizationUsed: params.optimization || "default",
        }
      );
    } catch (error) {
      return this.createErrorResponse(
        `Failed to compile asset catalog: ${params.catalogPath}`,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  private parseCompilationOutput(output: string): {
    processedAssets: number;
    generatedFiles: string[];
    totalSize: string;
  } {
    const lines = output.split("\n");
    const generatedFiles: string[] = [];
    let processedAssets = 0;

    for (const line of lines) {
      if (line.includes("/* compiled */")) {
        generatedFiles.push(line.trim());
      }
      if (line.includes("Processing")) {
        processedAssets++;
      }
    }

    return {
      processedAssets,
      generatedFiles,
      totalSize: "Unknown", // actool doesn't provide size info directly
    };
  }

  private extractWarnings(stderr: string): string[] {
    const warnings: string[] = [];
    const lines = stderr.split("\n");

    for (const line of lines) {
      if (line.includes("warning:")) {
        warnings.push(line.trim());
      }
    }

    return warnings;
  }
}

/**
 * Icon generation tool with multi-platform support
 */
export class IconGeneratorTool extends CommandToolBase<{
  sourceImage: string;
  outputPath: string;
  platform?: string;
  generateAllSizes?: boolean;
}> {
  constructor(server: XcodeServer) {
    super(
      server,
      "generate_icon_set",
      "Generate app icon sets from source image with multi-platform support",
      z.object({
        sourceImage: z
          .string()
          .describe("Path to the source image (should be at least 1024x1024)"),
        outputPath: z
          .string()
          .describe("Path where to create the AppIcon.appiconset directory"),
        platform: z
          .enum(["ios", "macos", "watchos"])
          .optional()
          .describe("Target platform"),
        generateAllSizes: z
          .boolean()
          .optional()
          .describe("Generate all required sizes for platform"),
      })
    );

    ToolRegistry.register(this.toolName, {
      category: ToolCategory.XCODE_UTILITIES,
      description: this.description,
      tags: ["icons", "generation", "assets"],
      complexity: "intermediate",
      requiresActiveProject: false,
      requiresXcode: false,
      platforms: ["ios", "macos", "watchos"],
      version: "2.0.0",
    });
  }

  protected getCommand(): string {
    return "sips";
  }

  protected buildArgs(params: {
    sourceImage: string;
    outputPath: string;
    platform?: string;
    generateAllSizes?: boolean;
  }): string[] {
    // sips will be called multiple times for different sizes
    return ["-g", "pixelWidth", "-g", "pixelHeight", params.sourceImage];
  }

  protected async executeImpl(params: {
    sourceImage: string;
    outputPath: string;
    platform?: string;
    generateAllSizes?: boolean;
  }): Promise<ToolResult> {
    try {
      // Validate source image
      const sourcePath = await this.validateAndResolvePath(
        params.sourceImage,
        "read"
      );

      // Check image dimensions
      const dimensionsResult = await this.executeToolCommand(params);
      const width = this.extractDimension(
        dimensionsResult.stdout,
        "pixelWidth"
      );
      const height = this.extractDimension(
        dimensionsResult.stdout,
        "pixelHeight"
      );

      if (width < 1024 || height < 1024) {
        return this.createErrorResponse(
          "Source image must be at least 1024x1024 pixels",
          new Error(`Current size: ${width}x${height}`)
        );
      }

      // Generate icon set
      const platform = params.platform || "ios";
      const iconSizes = this.getIconSizes(platform);
      const outputDir = `${params.outputPath}/AppIcon.appiconset`;

      // Create output directory
      const fs = await import("fs/promises");
      await fs.mkdir(outputDir, { recursive: true });

      // Generate each icon size
      const generatedIcons: Array<{
        size: string;
        filename: string;
        scale: string;
      }> = [];

      for (const iconSpec of iconSizes) {
        const filename = `icon_${iconSpec.size}@${iconSpec.scale}x.png`;
        const outputPath = `${outputDir}/${filename}`;

        await this.executeCommand("sips", [
          "-z",
          iconSpec.pixels.toString(),
          iconSpec.pixels.toString(),
          sourcePath,
          "--out",
          outputPath,
        ]);

        generatedIcons.push({
          size: iconSpec.size,
          filename,
          scale: iconSpec.scale,
        });
      }

      // Generate Contents.json
      const contentsJson = this.generateContentsJson(generatedIcons, platform);
      await fs.writeFile(
        `${outputDir}/Contents.json`,
        JSON.stringify(contentsJson, null, 2)
      );

      return this.createSuccessResponse(
        `Successfully generated ${generatedIcons.length} icon sizes for ${platform}`,
        {
          sourceImage: sourcePath,
          outputDirectory: outputDir,
          platform,
          generatedIcons,
          sourceImageSize: `${width}x${height}`,
        }
      );
    } catch (error) {
      return this.createErrorResponse(
        `Failed to generate icon set`,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  private extractDimension(output: string, dimension: string): number {
    const match = output.match(new RegExp(`${dimension}:\\s*(\\d+)`));
    return match ? parseInt(match[1], 10) : 0;
  }

  private getIconSizes(
    platform: string
  ): Array<{ size: string; scale: string; pixels: number }> {
    const sizes: Record<
      string,
      Array<{ size: string; scale: string; pixels: number }>
    > = {
      ios: [
        { size: "20x20", scale: "2", pixels: 40 },
        { size: "20x20", scale: "3", pixels: 60 },
        { size: "29x29", scale: "2", pixels: 58 },
        { size: "29x29", scale: "3", pixels: 87 },
        { size: "40x40", scale: "2", pixels: 80 },
        { size: "40x40", scale: "3", pixels: 120 },
        { size: "60x60", scale: "2", pixels: 120 },
        { size: "60x60", scale: "3", pixels: 180 },
        { size: "1024x1024", scale: "1", pixels: 1024 },
      ],
      macos: [
        { size: "16x16", scale: "1", pixels: 16 },
        { size: "16x16", scale: "2", pixels: 32 },
        { size: "32x32", scale: "1", pixels: 32 },
        { size: "32x32", scale: "2", pixels: 64 },
        { size: "128x128", scale: "1", pixels: 128 },
        { size: "128x128", scale: "2", pixels: 256 },
        { size: "256x256", scale: "1", pixels: 256 },
        { size: "256x256", scale: "2", pixels: 512 },
        { size: "512x512", scale: "1", pixels: 512 },
        { size: "512x512", scale: "2", pixels: 1024 },
      ],
      watchos: [
        { size: "24x24", scale: "2", pixels: 48 },
        { size: "27.5x27.5", scale: "2", pixels: 55 },
        { size: "29x29", scale: "2", pixels: 58 },
        { size: "29x29", scale: "3", pixels: 87 },
        { size: "40x40", scale: "2", pixels: 80 },
        { size: "44x44", scale: "2", pixels: 88 },
        { size: "50x50", scale: "2", pixels: 100 },
        { size: "1024x1024", scale: "1", pixels: 1024 },
      ],
    };

    return sizes[platform] || sizes.ios;
  }

  private generateContentsJson(
    icons: Array<{ size: string; filename: string; scale: string }>,
    platform: string
  ): any {
    return {
      images: icons.map((icon) => ({
        filename: icon.filename,
        idiom:
          platform === "macos"
            ? "mac"
            : platform === "watchos"
            ? "watch"
            : "iphone",
        scale: `${icon.scale}x`,
        size: icon.size,
      })),
      info: {
        author: "xcode-mcp-server",
        version: 1,
      },
    };
  }
}

/**
 * Register Xcode utility tools
 */
export function registerXcodeTools(server: XcodeServer): void {
  const xcodeInfoTool = new XcodeInfoTool(server);
  const assetCatalogTool = new AssetCatalogTool(server);
  const iconGeneratorTool = new IconGeneratorTool(server);

  xcodeInfoTool.register();
  assetCatalogTool.register();
  iconGeneratorTool.register();
}

/**
 * @deprecated Use registerXcodeTools instead
 */
export function registerAdvancedXcodeTools(server: XcodeServer): void {
  registerXcodeTools(server);
}

/**
 * @deprecated Use registerXcodeTools instead
 */
export function registerEnterpriseXcodeTools(server: XcodeServer): void {
  registerXcodeTools(server);
}

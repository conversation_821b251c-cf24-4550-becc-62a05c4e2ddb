/**
 * Command execution utilities and patterns
 * Extracted from common.ts for better organization
 */

/**
 * Time and date utilities
 */
export class TimeUtils {
  /**
   * Format a duration in milliseconds to a human-readable string
   */
  static formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  }

  /**
   * Create a debounced function
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;

    return (...args: Parameters<T>) => {
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  /**
   * Create a throttled function
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle = false;

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }

  /**
   * Sleep for a specified number of milliseconds
   */
  static sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get current timestamp in various formats
   */
  static getTimestamp(
    format: "iso" | "unix" | "readable" = "iso"
  ): string | number {
    const now = new Date();

    switch (format) {
      case "unix":
        return Math.floor(now.getTime() / 1000);
      case "readable":
        return now.toLocaleString();
      case "iso":
      default:
        return now.toISOString();
    }
  }

  /**
   * Calculate elapsed time between two dates
   */
  static getElapsed(start: Date, end: Date = new Date()): number {
    return end.getTime() - start.getTime();
  }

  /**
   * Format elapsed time in a human-readable way
   */
  static formatElapsed(start: Date, end: Date = new Date()): string {
    const elapsed = this.getElapsed(start, end);
    return this.formatDuration(elapsed);
  }
}

/**
 * Consolidated command execution patterns and utilities
 * This replaces duplicate functionality from FileHelpers
 */
export class CommandUtils {
  /**
   * Execute find command with enhanced patterns and filtering
   * Consolidated implementation that replaces duplicate code
   */
  static async findFiles(
    searchPath: string,
    patterns: string | string[],
    options: {
      type?: "f" | "d" | "l";
      maxDepth?: number;
      includeHidden?: boolean;
      timeout?: number;
      caseSensitive?: boolean;
    } = {}
  ): Promise<string[]> {
    const {
      type = "f",
      maxDepth,
      includeHidden = false,
      timeout = 30000,
      caseSensitive = false,
    } = options;

    const patternsArray = Array.isArray(patterns) ? patterns : [patterns];
    const results: string[] = [];

    for (const pattern of patternsArray) {
      const args = [searchPath];

      if (maxDepth !== undefined) {
        args.push("-maxdepth", maxDepth.toString());
      }

      args.push("-type", type);

      if (!includeHidden) {
        args.push("-not", "-path", "*/\\.*");
      }

      // Handle case sensitivity
      if (caseSensitive) {
        args.push("-name", pattern);
      } else {
        args.push("-iname", pattern);
      }

      try {
        const { SecureCommandExecutor } = await import("./commandExecutor.js");
        const { stdout } = await SecureCommandExecutor.execute("find", args, {
          timeout,
        });

        const files = stdout.trim().split("\n").filter(Boolean);
        results.push(...files);
      } catch (error) {
        // Continue with other patterns if one fails
        console.warn(`Failed to find files with pattern ${pattern}:`, error);
      }
    }

    return [...new Set(results)].sort(); // Remove duplicates and sort
  }

  /**
   * Execute find command for project files with common patterns
   */
  static async findProjectFiles(
    searchPath: string,
    extensions: string[],
    options: {
      maxDepth?: number;
      includeHidden?: boolean;
      timeout?: number;
      caseSensitive?: boolean;
    } = {}
  ): Promise<string[]> {
    const patterns = extensions.map((ext) =>
      ext.startsWith("*.") ? ext : `*.${ext}`
    );

    return this.findFiles(searchPath, patterns, {
      ...options,
      type: "f",
    });
  }

  /**
   * Parse command output into structured data
   */
  static parseCommandOutput(
    output: string,
    options: {
      splitLines?: boolean;
      filterEmpty?: boolean;
      trim?: boolean;
    } = {}
  ): string[] {
    const { splitLines = true, filterEmpty = true, trim = true } = options;

    if (!splitLines) {
      return [trim ? output.trim() : output];
    }

    let lines = output.split("\n");

    if (trim) {
      lines = lines.map((line) => line.trim());
    }

    if (filterEmpty) {
      lines = lines.filter(Boolean);
    }

    return lines;
  }

  /**
   * Execute a command with retry logic
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: {
      maxRetries?: number;
      delay?: number;
      backoff?: boolean;
      retryCondition?: (error: any) => boolean;
    } = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      delay = 1000,
      backoff = true,
      retryCondition = () => true,
    } = options;

    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries || !retryCondition(error)) {
          throw error;
        }

        const waitTime = backoff ? delay * Math.pow(2, attempt) : delay;
        await TimeUtils.sleep(waitTime);
      }
    }

    throw lastError;
  }

  /**
   * Execute multiple commands in parallel with concurrency control
   */
  static async executeParallel<T>(
    operations: Array<() => Promise<T>>,
    options: {
      concurrency?: number;
      failFast?: boolean;
    } = {}
  ): Promise<Array<T | Error>> {
    const { concurrency = 5, failFast = false } = options;
    const results: Array<T | Error> = [];
    const executing: Promise<void>[] = [];

    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i];

      const promise = operation()
        .then((result) => {
          results[i] = result;
        })
        .catch((error) => {
          if (failFast) {
            throw error;
          }
          results[i] = error;
        });

      executing.push(promise);

      if (executing.length >= concurrency) {
        await Promise.race(executing);
        executing.splice(
          executing.findIndex((p) => p === promise),
          1
        );
      }
    }

    await Promise.all(executing);
    return results;
  }

  /**
   * Create a command timeout wrapper
   */
  static withTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number,
    timeoutMessage = "Operation timed out"
  ): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs);
      }),
    ]);
  }
}

/**
 * Secure message formatting utilities to prevent information leakage
 */
export class SecureMessageFormatter {
  /**
   * Format an error message safely, sanitizing sensitive information
   */
  static formatError(
    message: string,
    context?: Record<string, unknown>
  ): string {
    let sanitizedMessage = this.sanitizeForLogging(message);

    if (context) {
      const sanitizedContext = this.sanitizeContext(context);
      if (Object.keys(sanitizedContext).length > 0) {
        sanitizedMessage += ` Context: ${JSON.stringify(sanitizedContext)}`;
      }
    }

    return sanitizedMessage;
  }

  /**
   * Format a success message with optional data
   */
  static formatSuccess(message: string, data?: unknown): string {
    let formattedMessage = this.sanitizeForLogging(message);

    if (data) {
      const sanitizedData = this.sanitizeData(data);
      if (sanitizedData) {
        formattedMessage += `\n${sanitizedData}`;
      }
    }

    return formattedMessage;
  }

  /**
   * Format a path for safe display (hides sensitive directory information)
   */
  static formatPath(filePath: string): string {
    return this.sanitizeForLogging(filePath);
  }

  /**
   * Format command output safely
   */
  static formatCommandOutput(stdout: string, stderr?: string): string {
    let output = this.sanitizeForLogging(stdout);

    if (stderr) {
      const sanitizedStderr = this.sanitizeForLogging(stderr);
      if (sanitizedStderr.trim()) {
        output += `\nError output:\n${sanitizedStderr}`;
      }
    }

    return output;
  }

  /**
   * Sanitize a string for safe logging (removes potential sensitive data)
   * @deprecated Use ValidationUtils.sanitizeForLogging instead
   */
  static sanitizeForLogging(input: string): string {
    // Import and delegate to the centralized implementation
    const { ValidationUtils } = require("./validationUtilities.js");
    return ValidationUtils.sanitizeForLogging(input);
  }

  /**
   * Sanitize context object for logging
   */
  private static sanitizeContext(
    context: Record<string, unknown>
  ): Record<string, unknown> {
    const sanitized: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(context)) {
      if (typeof value === "string") {
        sanitized[key] = this.sanitizeForLogging(value);
      } else if (typeof value === "object" && value !== null) {
        sanitized[key] = "[Object]";
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Sanitize data for safe display
   */
  private static sanitizeData(data: unknown): string | null {
    if (typeof data === "string") {
      return this.sanitizeForLogging(data);
    } else if (typeof data === "object" && data !== null) {
      try {
        const sanitizedObj = this.sanitizeContext(
          data as Record<string, unknown>
        );
        return JSON.stringify(sanitizedObj, null, 2);
      } catch {
        return "[Complex Object]";
      }
    } else {
      return String(data);
    }
  }
}

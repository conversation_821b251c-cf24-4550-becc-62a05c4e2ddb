import * as fs from "fs/promises";
import * as path from "path";
import { PathManager } from "./pathManager.js";
import { SecureCommandExecutor } from "./commandExecutor.js";
import { FileOperationError, PathAccessError } from "./errors.js";
import { StringUtils } from "./stringUtilities.js";

/**
 * Comprehensive file system manager that consolidates all file operations
 * Combines functionality from file.ts, fileHelpers.ts, and safeFileOperations.ts
 */
export class FileSystemManager {
  constructor(private pathManager?: PathManager) {}

  /**
   * Check if a file or directory exists
   */
  async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file stats with enhanced error handling
   */
  async getStats(filePath: string) {
    try {
      return await fs.stat(filePath);
    } catch (error) {
      throw new FileOperationError(
        "stat",
        filePath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Check if path is a directory
   */
  async isDirectory(filePath: string): Promise<boolean> {
    try {
      const stats = await this.getStats(filePath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  /**
   * Check if path is a file
   */
  async isFile(filePath: string): Promise<boolean> {
    try {
      const stats = await this.getStats(filePath);
      return stats.isFile();
    } catch {
      return false;
    }
  }

  /**
   * Format file size in human-readable format
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Get MIME type for file extension (consolidated from multiple sources)
   */
  getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      ".swift": "text/x-swift",
      ".m": "text/x-objective-c",
      ".h": "text/x-c",
      ".c": "text/x-c",
      ".cpp": "text/x-c++",
      ".json": "application/json",
      ".plist": "application/x-plist",
      ".storyboard": "application/x-xcode-storyboard",
      ".xib": "application/x-xcode-xib",
      ".txt": "text/plain",
      ".md": "text/markdown",
      ".js": "application/javascript",
      ".ts": "application/typescript",
      ".html": "text/html",
      ".css": "text/css",
      ".xml": "application/xml",
      ".yaml": "application/x-yaml",
      ".yml": "application/x-yaml",
      ".png": "image/png",
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".gif": "image/gif",
      ".pdf": "application/pdf",
    };

    return mimeTypes[extension.toLowerCase()] || "application/octet-stream";
  }

  /**
   * Check if a path is allowed based on config and active project
   */
  isPathAllowed(
    targetPath: string,
    projectsBaseDir?: string,
    activeProjectPath?: string
  ): boolean {
    // If projectsBaseDir is set, allow paths within it
    if (projectsBaseDir) {
      if (
        targetPath === projectsBaseDir ||
        targetPath.startsWith(projectsBaseDir + path.sep)
      ) {
        return true;
      }
    }

    // If there's an active project, allow paths within its directory
    if (activeProjectPath) {
      const projectDir = path.dirname(activeProjectPath);
      if (
        targetPath === projectDir ||
        targetPath.startsWith(projectDir + path.sep)
      ) {
        return true;
      }
    }

    // Allow paths within the server's directory for development purposes
    const serverDir = process.cwd();
    if (
      targetPath === serverDir ||
      targetPath.startsWith(serverDir + path.sep)
    ) {
      return true;
    }

    return false;
  }

  /**
   * Read file with comprehensive options and security validation
   */
  async readFile(
    filePath: string,
    options: {
      encoding?: BufferEncoding;
      asBinary?: boolean;
      maxSize?: number;
      validatePath?: boolean;
    } = {}
  ): Promise<{
    content: string;
    isBinary: boolean;
    size: number;
    mimeType: string;
  }> {
    const {
      encoding = "utf-8",
      asBinary = false,
      maxSize = 10 * 1024 * 1024,
      validatePath = true,
    } = options;

    let validatedPath = filePath;

    // Validate path if PathManager is available and validation is requested
    if (this.pathManager && validatePath) {
      validatedPath = this.pathManager.validatePathForReading(filePath);
    }

    const stats = await this.getStats(validatedPath);

    if (stats.size > maxSize) {
      throw new FileOperationError(
        "read",
        validatedPath,
        new Error(
          `File size (${this.formatFileSize(
            stats.size
          )}) exceeds maximum allowed size (${this.formatFileSize(maxSize)})`
        )
      );
    }

    try {
      const ext = path.extname(validatedPath);
      const mimeType = this.getMimeType(ext);

      if (asBinary) {
        const buffer = await fs.readFile(validatedPath);
        return {
          content: buffer.toString("base64"),
          isBinary: true,
          size: stats.size,
          mimeType,
        };
      } else {
        const content = await fs.readFile(validatedPath, { encoding });
        return {
          content,
          isBinary: false,
          size: stats.size,
          mimeType,
        };
      }
    } catch (error) {
      if (error instanceof Error) {
        const nodeError = error as NodeJS.ErrnoException;
        if (nodeError.code === "ENOENT") {
          throw new FileOperationError(
            "read",
            validatedPath,
            new Error("File does not exist")
          );
        }
        if (nodeError.code === "EACCES") {
          throw new FileOperationError(
            "read",
            validatedPath,
            new Error("Permission denied")
          );
        }
      }
      throw new FileOperationError(
        "read",
        validatedPath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Write file with comprehensive options and security validation
   */
  async writeFile(
    filePath: string,
    content: string,
    options: {
      encoding?: BufferEncoding;
      fromBase64?: boolean;
      createPath?: boolean;
      backup?: boolean;
      validatePath?: boolean;
      createIfMissing?: boolean;
    } = {}
  ): Promise<void> {
    const {
      encoding = "utf-8",
      fromBase64 = false,
      createPath = true,
      backup = false,
      validatePath = true,
      createIfMissing = true,
    } = options;

    let validatedPath = filePath;

    // Validate path if PathManager is available and validation is requested
    if (this.pathManager && validatePath) {
      validatedPath = this.pathManager.validatePathForWriting(filePath);
    }

    try {
      // Check if the file exists
      const exists = await this.exists(validatedPath);
      if (!exists && !createIfMissing) {
        throw new FileOperationError(
          "write",
          validatedPath,
          new Error("File does not exist and createIfMissing is false")
        );
      }

      // Create directory if needed
      if (createPath) {
        await fs.mkdir(path.dirname(validatedPath), { recursive: true });
      }

      // Create backup if requested
      if (backup && exists) {
        const backupPath = `${validatedPath}.backup.${Date.now()}`;
        await fs.copyFile(validatedPath, backupPath);
      }

      // Write file
      if (fromBase64) {
        const buffer = Buffer.from(content, "base64");
        await fs.writeFile(validatedPath, buffer);
      } else {
        await fs.writeFile(validatedPath, content, { encoding });
      }
    } catch (error) {
      if (error instanceof FileOperationError) {
        throw error;
      }

      if (error instanceof Error) {
        const nodeError = error as NodeJS.ErrnoException;
        if (nodeError.code === "EACCES") {
          throw new FileOperationError(
            "write",
            validatedPath,
            new Error("Permission denied")
          );
        }
        if (nodeError.code === "EISDIR") {
          throw new FileOperationError(
            "write",
            validatedPath,
            new Error("Path is a directory, not a file")
          );
        }
      }

      throw new FileOperationError(
        "write",
        validatedPath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * List directory contents with comprehensive options and security validation
   */
  async listDirectory(
    dirPath: string,
    options: {
      validatePath?: boolean;
      projectsBaseDir?: string;
      activeProjectPath?: string;
    } = {}
  ): Promise<string[]> {
    const { validatePath = true, projectsBaseDir, activeProjectPath } = options;

    let validatedPath = dirPath;

    // Validate path if PathManager is available and validation is requested
    if (this.pathManager && validatePath) {
      validatedPath = this.pathManager.validatePathForReading(dirPath);
    } else if (!validatePath) {
      // Use legacy path validation if PathManager is not available
      const targetPath = path.resolve(dirPath);
      if (!this.isPathAllowed(targetPath, projectsBaseDir, activeProjectPath)) {
        throw new PathAccessError(targetPath);
      }
      validatedPath = targetPath;
    }

    try {
      const entries = await fs.readdir(validatedPath, { withFileTypes: true });
      return entries.map((entry) => {
        const fullPath = path.join(validatedPath, entry.name);
        return `${entry.isDirectory() ? "d" : "f"} ${fullPath}`;
      });
    } catch (error) {
      if (error instanceof Error) {
        const nodeError = error as NodeJS.ErrnoException;
        if (nodeError.code === "ENOENT") {
          throw new FileOperationError(
            "list",
            validatedPath,
            new Error("Directory does not exist")
          );
        }
        if (nodeError.code === "EACCES") {
          throw new FileOperationError(
            "list",
            validatedPath,
            new Error("Permission denied")
          );
        }
        if (nodeError.code === "ENOTDIR") {
          throw new FileOperationError(
            "list",
            validatedPath,
            new Error("Path is not a directory")
          );
        }
      }
      throw new FileOperationError(
        "list",
        validatedPath,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Check if a file is inside an Xcode project
   */
  async isInXcodeProject(filePath: string): Promise<boolean> {
    const dir = path.dirname(filePath);
    const parentDirs = dir.split(path.sep);

    // Check each parent directory for .xcodeproj or .xcworkspace
    while (parentDirs.length > 0) {
      const currentPath = parentDirs.join(path.sep);
      try {
        const entries = await fs.readdir(currentPath);
        if (
          entries.some(
            (entry) =>
              entry.endsWith(".xcodeproj") || entry.endsWith(".xcworkspace")
          )
        ) {
          return true;
        }
      } catch {
        // Ignore read errors, just continue checking
      }
      parentDirs.pop();
    }

    return false;
  }

  /**
   * Copy file or directory with enhanced options
   */
  async copy(
    source: string,
    destination: string,
    options: {
      recursive?: boolean;
      overwrite?: boolean;
      preserveTimestamps?: boolean;
    } = {}
  ): Promise<void> {
    const {
      recursive = false,
      overwrite = false,
      preserveTimestamps = true,
    } = options;

    const sourceExists = await this.exists(source);
    if (!sourceExists) {
      throw new FileOperationError(
        "copy",
        source,
        new Error("Source does not exist")
      );
    }

    const isSourceDir = await this.isDirectory(source);

    if (isSourceDir && !recursive) {
      throw new FileOperationError(
        "copy",
        source,
        new Error(
          "Source is a directory. Use recursive=true to copy directories."
        )
      );
    }

    const destExists = await this.exists(destination);
    if (destExists && !overwrite) {
      throw new FileOperationError(
        "copy",
        destination,
        new Error("Destination exists and overwrite=false")
      );
    }

    try {
      if (isSourceDir) {
        // Use system cp command for directories
        const args = ["-R"];
        if (preserveTimestamps) args.push("-p");
        args.push(source, destination);

        await SecureCommandExecutor.execute("cp", args);
      } else {
        // Use fs.copyFile for files
        await fs.copyFile(source, destination);

        if (preserveTimestamps) {
          const stats = await this.getStats(source);
          await fs.utimes(destination, stats.atime, stats.mtime);
        }
      }
    } catch (error) {
      throw new FileOperationError(
        "copy",
        source,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Find files with enhanced patterns and filtering
   */
  async findFiles(
    searchPath: string,
    patterns: string | string[],
    options: {
      maxDepth?: number;
      includeHidden?: boolean;
      type?: "f" | "d" | "l";
      timeout?: number;
      caseSensitive?: boolean;
    } = {}
  ): Promise<string[]> {
    const {
      type = "f",
      maxDepth,
      includeHidden = false,
      timeout = 30000,
      caseSensitive = false,
    } = options;

    const patternsArray = Array.isArray(patterns) ? patterns : [patterns];
    const results: string[] = [];

    for (const pattern of patternsArray) {
      const args = [searchPath];

      if (maxDepth !== undefined) {
        args.push("-maxdepth", maxDepth.toString());
      }

      args.push("-type", type);

      if (!includeHidden) {
        args.push("-not", "-path", "*/\\.*");
      }

      // Handle case sensitivity
      if (caseSensitive) {
        args.push("-name", pattern);
      } else {
        args.push("-iname", pattern);
      }

      try {
        const { stdout } = await SecureCommandExecutor.execute("find", args, {
          timeout,
        });

        const files = stdout.trim().split("\n").filter(Boolean);
        results.push(...files);
      } catch (error) {
        // Continue with other patterns if one fails
        console.warn(`Failed to find files with pattern ${pattern}:`, error);
      }
    }

    return [...new Set(results)]; // Remove duplicates
  }

  /**
   * Search for text within files
   */
  async searchInFiles(
    searchPath: string,
    searchText: string,
    filePattern: string,
    options: {
      isRegex?: boolean;
      caseSensitive?: boolean;
      maxResults?: number;
      includeHidden?: boolean;
    } = {}
  ): Promise<
    Array<{
      file: string;
      matches: Array<{ line: number; content: string; match: string }>;
    }>
  > {
    const {
      isRegex = false,
      caseSensitive = false,
      maxResults = 100,
      includeHidden = false,
    } = options;

    // Find matching files
    const files = await this.findFiles(searchPath, filePattern, {
      type: "f",
      includeHidden,
    });

    if (files.length === 0) {
      return [];
    }

    // Prepare search regex
    let searchRegex: RegExp;
    if (isRegex) {
      try {
        searchRegex = new RegExp(searchText, caseSensitive ? "g" : "gi");
      } catch (error) {
        throw new Error(
          `Invalid regular expression: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    } else {
      const escapedText = StringUtils.escapeRegExp(searchText);
      searchRegex = new RegExp(escapedText, caseSensitive ? "g" : "gi");
    }

    const results: Array<{
      file: string;
      matches: Array<{ line: number; content: string; match: string }>;
    }> = [];

    let totalMatches = 0;

    for (const file of files) {
      if (totalMatches >= maxResults) break;

      try {
        const { content } = await this.readFile(file, {
          maxSize: 1024 * 1024, // 1MB limit
          validatePath: false, // Skip validation for search operations
        });
        const lines = content.split("\n");
        const matches: Array<{ line: number; content: string; match: string }> =
          [];

        for (let i = 0; i < lines.length && totalMatches < maxResults; i++) {
          const line = lines[i];
          const lineMatches = Array.from(line.matchAll(searchRegex));

          for (const match of lineMatches) {
            if (totalMatches >= maxResults) break;

            matches.push({
              line: i + 1,
              content: line,
              match: match[0],
            });
            totalMatches++;
          }
        }

        if (matches.length > 0) {
          results.push({ file, matches });
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    return results;
  }
}

// Export legacy functions for backward compatibility
export const isPathAllowed = (
  targetPath: string,
  projectsBaseDir?: string,
  activeProjectPath?: string
): boolean => {
  const fsManager = new FileSystemManager();
  return fsManager.isPathAllowed(
    targetPath,
    projectsBaseDir,
    activeProjectPath
  );
};

export const getMimeTypeForExtension = (ext: string): string => {
  const fsManager = new FileSystemManager();
  return fsManager.getMimeType(ext);
};

export const listDirectory = async (
  dirPath: string,
  projectsBaseDir?: string,
  activeProjectPath?: string
): Promise<string[]> => {
  const fsManager = new FileSystemManager();
  return fsManager.listDirectory(dirPath, {
    validatePath: false,
    projectsBaseDir,
    activeProjectPath,
  });
};

export const isInXcodeProject = async (filePath: string): Promise<boolean> => {
  const fsManager = new FileSystemManager();
  return fsManager.isInXcodeProject(filePath);
};

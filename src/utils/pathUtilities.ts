/**
 * Path manipulation utilities
 * Extracted from common.ts for better organization
 */

import * as path from "path";
import * as os from "os";

/**
 * Path manipulation utilities - Centralized and enhanced
 */
export class PathUtils {
  /**
   * Normalize a path and resolve environment variables
   * This is the canonical implementation used throughout the codebase
   */
  static expandPath(inputPath: string): string {
    if (!inputPath) return inputPath;

    // Handle tilde expansion
    if (inputPath.startsWith("~")) {
      inputPath = path.join(os.homedir(), inputPath.slice(1));
    }

    // Handle environment variables - $VAR format
    inputPath = inputPath.replace(/\$([A-Za-z_][A-Za-z0-9_]*)/g, (_, name) => {
      return process.env[name] || "";
    });

    // Handle environment variables - ${VAR} format
    inputPath = inputPath.replace(
      /\${([A-Za-z_][A-Za-z0-9_]*)}/g,
      (_, name) => {
        return process.env[name] || "";
      }
    );

    return path.resolve(inputPath);
  }

  /**
   * Check if a path is a subdirectory of another path
   */
  static isSubdirectory(parent: string, child: string): boolean {
    const relative = path.relative(parent, child);
    return !relative.startsWith("..") && !path.isAbsolute(relative);
  }

  /**
   * Get the common ancestor path of multiple paths
   */
  static getCommonPath(paths: string[]): string {
    if (paths.length === 0) return "";
    if (paths.length === 1) return path.dirname(paths[0]);

    const normalizedPaths = paths.map((p) => path.resolve(p));
    const parts = normalizedPaths[0].split(path.sep);

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      if (!normalizedPaths.every((p) => p.split(path.sep)[i] === part)) {
        return parts.slice(0, i).join(path.sep) || path.sep;
      }
    }

    return normalizedPaths[0];
  }

  /**
   * Join paths safely
   */
  static joinPaths(...paths: string[]): string {
    return path.join(...paths);
  }

  /**
   * Get the file extension from a path
   */
  static getExtension(filePath: string): string {
    return path.extname(filePath);
  }

  /**
   * Get the filename without extension
   */
  static getBasename(filePath: string, ext?: string): string {
    return path.basename(filePath, ext);
  }

  /**
   * Get the directory name from a path
   */
  static getDirname(filePath: string): string {
    return path.dirname(filePath);
  }

  /**
   * Check if a path is absolute
   */
  static isAbsolute(inputPath: string): boolean {
    return path.isAbsolute(inputPath);
  }

  /**
   * Convert a path to use forward slashes (useful for URLs or cross-platform compatibility)
   */
  static toForwardSlashes(inputPath: string): string {
    return inputPath.replace(/\\/g, "/");
  }

  /**
   * Convert a path to use the platform-specific separator
   */
  static toPlatformPath(inputPath: string): string {
    return inputPath.replace(/[/\\]/g, path.sep);
  }

  /**
   * Check if a path exists and is accessible
   */
  static async exists(inputPath: string): Promise<boolean> {
    try {
      const fs = await import("fs/promises");
      await fs.access(inputPath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get the size of a file or directory
   */
  static async getSize(inputPath: string): Promise<number> {
    const fs = await import("fs/promises");
    const stats = await fs.stat(inputPath);

    if (stats.isFile()) {
      return stats.size;
    } else if (stats.isDirectory()) {
      let totalSize = 0;
      const entries = await fs.readdir(inputPath, { withFileTypes: true });

      for (const entry of entries) {
        const entryPath = path.join(inputPath, entry.name);
        if (entry.isFile()) {
          const entryStats = await fs.stat(entryPath);
          totalSize += entryStats.size;
        } else if (entry.isDirectory()) {
          totalSize += await this.getSize(entryPath);
        }
      }

      return totalSize;
    }

    return 0;
  }

  /**
   * Create a directory recursively
   */
  static async ensureDir(dirPath: string): Promise<void> {
    const fs = await import("fs/promises");
    await fs.mkdir(dirPath, { recursive: true });
  }

  /**
   * Get the home directory path
   */
  static getHomeDir(): string {
    return os.homedir();
  }

  /**
   * Get the temporary directory path
   */
  static getTempDir(): string {
    return os.tmpdir();
  }

  /**
   * Generate a unique temporary file path
   */
  static getTempFilePath(prefix = "tmp", suffix = ""): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const filename = `${prefix}_${timestamp}_${random}${suffix}`;
    return path.join(this.getTempDir(), filename);
  }

  /**
   * Check if a path points to a hidden file or directory (starts with .)
   */
  static isHidden(inputPath: string): boolean {
    const basename = path.basename(inputPath);
    return basename.startsWith(".") && basename !== "." && basename !== "..";
  }

  /**
   * Get all parent directories of a path
   */
  static getParentDirs(inputPath: string): string[] {
    const parents: string[] = [];
    let current = path.resolve(inputPath);

    while (true) {
      const parent = path.dirname(current);
      if (parent === current) break; // Reached root
      parents.push(parent);
      current = parent;
    }

    return parents;
  }

  /**
   * Find the nearest parent directory containing a specific file
   */
  static async findNearestFile(
    startPath: string,
    filename: string
  ): Promise<string | null> {
    const parents = [path.resolve(startPath), ...this.getParentDirs(startPath)];

    for (const parent of parents) {
      const filePath = path.join(parent, filename);
      if (await this.exists(filePath)) {
        return filePath;
      }
    }

    return null;
  }

  /**
   * Get file paths matching a glob pattern
   */
  static async glob(
    pattern: string,
    options: { cwd?: string; absolute?: boolean } = {}
  ): Promise<string[]> {
    try {
      const { glob } = await import("glob");
      const { cwd = process.cwd(), absolute = false } = options;

      return await glob(pattern, { cwd, absolute });
    } catch (error) {
      console.warn("Glob pattern matching failed:", error);
      return [];
    }
  }
}

/**
 * Security utilities for input validation and sanitization
 * Addresses security vulnerabilities identified in the audit
 */

import { ValidationError } from "./errors.js";
import { ValidationUtils } from "./validationUtilities.js";
import { StringUtils } from "./stringUtilities.js";

/**
 * Security validation levels
 */
export enum SecurityLevel {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
}

/**
 * Input sanitization and validation utilities
 */
export class SecurityUtils {
  /**
   * Validate and sanitize a file path
   */
  static validateFilePath(filePath: string, allowRelative = true): string {
    if (!filePath || typeof filePath !== "string") {
      throw new ValidationError(
        "filePath",
        filePath,
        "File path must be a non-empty string"
      );
    }

    // Check for null bytes and other dangerous characters
    if (filePath.includes("\0")) {
      throw new ValidationError(
        "filePath",
        filePath,
        "File path contains null bytes"
      );
    }

    // Check for path traversal attempts
    if (filePath.includes("..")) {
      throw new ValidationError(
        "filePath",
        filePath,
        "Path traversal not allowed"
      );
    }

    // Check for dangerous patterns
    const dangerousPatterns = [
      /\/\.\./, // Path traversal
      /\.\.\//, // Path traversal
      /\0/, // Null bytes
      /[<>:"|?*]/, // Windows invalid characters
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(filePath)) {
        throw new ValidationError(
          "filePath",
          filePath,
          "File path contains invalid characters"
        );
      }
    }

    // Validate relative paths if not allowed
    if (
      !allowRelative &&
      !filePath.startsWith("/") &&
      !filePath.match(/^[A-Za-z]:/)
    ) {
      throw new ValidationError(
        "filePath",
        filePath,
        "Relative paths not allowed"
      );
    }

    return filePath.trim();
  }

  /**
   * Validate a scheme or target name
   */
  static validateIdentifier(
    identifier: string,
    fieldName = "identifier"
  ): string {
    if (!identifier || typeof identifier !== "string") {
      throw new ValidationError(
        fieldName,
        identifier,
        "Identifier must be a non-empty string"
      );
    }

    if (!ValidationUtils.isSafeIdentifier(identifier)) {
      throw new ValidationError(
        fieldName,
        identifier,
        "Identifier contains invalid characters"
      );
    }

    return identifier.trim();
  }

  /**
   * Validate command arguments to prevent injection
   */
  static validateCommandArgs(args: string[]): string[] {
    if (!Array.isArray(args)) {
      throw new ValidationError("args", args, "Arguments must be an array");
    }

    return args.map((arg, index) => {
      if (typeof arg !== "string") {
        throw new ValidationError(
          `args[${index}]`,
          arg,
          "All arguments must be strings"
        );
      }

      // Check for command injection patterns
      const dangerousPatterns = [
        /[;&|`$()]/, // Command separators and substitution
        /\$\(/, // Command substitution
        /`/, // Backticks
        /\|\|/, // OR operator
        /&&/, // AND operator
      ];

      for (const pattern of dangerousPatterns) {
        if (pattern.test(arg)) {
          throw new ValidationError(
            `args[${index}]`,
            arg,
            "Argument contains potentially dangerous characters"
          );
        }
      }

      return arg;
    });
  }

  /**
   * Sanitize output for safe display
   */
  static sanitizeOutput(output: string): string {
    if (typeof output !== "string") {
      return String(output);
    }

    return ValidationUtils.sanitizeForLogging(output);
  }

  /**
   * Validate parameters based on security level
   */
  static validateParameters(
    params: Record<string, unknown>,
    securityLevel: SecurityLevel = SecurityLevel.MEDIUM
  ): Record<string, unknown> {
    const validated: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(params)) {
      // Path validation
      if (key.toLowerCase().includes("path") && typeof value === "string") {
        validated[key] = this.validateFilePath(
          value,
          securityLevel !== SecurityLevel.HIGH
        );
      }
      // Identifier validation
      else if (
        (key.toLowerCase().includes("scheme") ||
          key.toLowerCase().includes("target") ||
          key.toLowerCase().includes("configuration")) &&
        typeof value === "string"
      ) {
        validated[key] = this.validateIdentifier(value, key);
      }
      // Array validation
      else if (Array.isArray(value)) {
        if (value.every((item) => typeof item === "string")) {
          validated[key] = this.validateCommandArgs(value as string[]);
        } else {
          validated[key] = value;
        }
      }
      // String sanitization
      else if (typeof value === "string") {
        validated[key] = this.sanitizeString(value, securityLevel);
      }
      // Pass through other types
      else {
        validated[key] = value;
      }
    }

    return validated;
  }

  /**
   * Sanitize a string based on security level
   */
  private static sanitizeString(
    input: string,
    securityLevel: SecurityLevel
  ): string {
    let sanitized = input;

    // Basic sanitization for all levels
    sanitized = sanitized.replace(/\0/g, ""); // Remove null bytes

    if (securityLevel === SecurityLevel.HIGH) {
      // Strict sanitization
      sanitized = sanitized.replace(/[<>:"|?*]/g, ""); // Remove dangerous characters
      sanitized = sanitized.replace(/\.\./g, ""); // Remove path traversal
    } else if (securityLevel === SecurityLevel.MEDIUM) {
      // Moderate sanitization
      sanitized = sanitized.replace(/\.\.\//g, ""); // Remove path traversal patterns
    }

    return sanitized.trim();
  }

  /**
   * Create a secure error message that doesn't leak sensitive information
   */
  static createSecureErrorMessage(
    error: Error | string,
    context?: Record<string, unknown>
  ): string {
    const message = error instanceof Error ? error.message : error;
    const sanitizedMessage = this.sanitizeOutput(message);

    if (context) {
      const sanitizedContext = this.sanitizeContext(context);
      if (Object.keys(sanitizedContext).length > 0) {
        return `${sanitizedMessage} Context: ${JSON.stringify(
          sanitizedContext
        )}`;
      }
    }

    return sanitizedMessage;
  }

  /**
   * Sanitize context object for logging
   */
  private static sanitizeContext(
    context: Record<string, unknown>
  ): Record<string, unknown> {
    const sanitized: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(context)) {
      if (typeof value === "string") {
        sanitized[key] = this.sanitizeOutput(value);
      } else if (typeof value === "object" && value !== null) {
        sanitized[key] = "[Object]";
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Validate URL for safe usage
   */
  static validateUrl(url: string): string {
    if (!url || typeof url !== "string") {
      throw new ValidationError("url", url, "URL must be a non-empty string");
    }

    if (!StringUtils.isUrl(url)) {
      throw new ValidationError("url", url, "Invalid URL format");
    }

    // Check for dangerous protocols
    const dangerousProtocols = ["javascript:", "data:", "vbscript:", "file:"];
    const lowerUrl = url.toLowerCase();

    for (const protocol of dangerousProtocols) {
      if (lowerUrl.startsWith(protocol)) {
        throw new ValidationError(
          "url",
          url,
          "Dangerous URL protocol not allowed"
        );
      }
    }

    return url;
  }

  /**
   * Validate email address
   */
  static validateEmail(email: string): string {
    if (!email || typeof email !== "string") {
      throw new ValidationError(
        "email",
        email,
        "Email must be a non-empty string"
      );
    }

    if (!StringUtils.isEmail(email)) {
      throw new ValidationError("email", email, "Invalid email format");
    }

    return email.trim().toLowerCase();
  }

  /**
   * Validate numeric input with bounds
   */
  static validateNumber(
    value: unknown,
    fieldName: string,
    min?: number,
    max?: number
  ): number {
    if (typeof value !== "number" || isNaN(value)) {
      throw new ValidationError(fieldName, value, "Must be a valid number");
    }

    if (min !== undefined && value < min) {
      throw new ValidationError(fieldName, value, `Must be at least ${min}`);
    }

    if (max !== undefined && value > max) {
      throw new ValidationError(fieldName, value, `Must be at most ${max}`);
    }

    return value;
  }
}

/**
 * Advanced error message sanitization to prevent information leakage
 */
export class SecureErrorFormatter {
  private static readonly SENSITIVE_PATTERNS = [
    // Absolute paths
    { pattern: /\/[A-Za-z0-9_\-\.\/]+/g, replacement: "[PATH_REDACTED]" },
    // User directories
    { pattern: /\/Users\/<USER>\/\s]+/g, replacement: "/Users/<USER>" },
    // Home directory references
    { pattern: /~\/[^\/\s]*/g, replacement: "~/[PATH]" },
    // Environment variables
    { pattern: /\$[A-Z_][A-Z0-9_]*/g, replacement: "$[ENV_VAR]" },
    // IP addresses
    { pattern: /\b(?:\d{1,3}\.){3}\d{1,3}\b/g, replacement: "[IP_ADDRESS]" },
    // UUIDs
    {
      pattern: /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi,
      replacement: "[UUID]",
    },
    // API keys (common patterns)
    { pattern: /[A-Za-z0-9]{32,}/g, replacement: "[API_KEY]" },
    // File extensions with sensitive info
    {
      pattern: /\.(key|pem|p12|mobileprovision)(\s|$)/gi,
      replacement: ".[CERT_FILE]$1",
    },
  ];

  private static readonly PATH_CONTEXT_MAP = new Map<string, string>();

  /**
   * Sanitize error message with advanced pattern matching
   */
  static sanitizeErrorMessage(
    message: string,
    context?: Record<string, unknown>,
    options: {
      preserveFileNames?: boolean;
      preserveExtensions?: boolean;
      includeContext?: boolean;
    } = {}
  ): string {
    let sanitizedMessage = message;

    // Apply all sensitive pattern replacements
    for (const { pattern, replacement } of this.SENSITIVE_PATTERNS) {
      sanitizedMessage = sanitizedMessage.replace(pattern, replacement);
    }

    // Preserve file names if requested (but still sanitize paths)
    if (options.preserveFileNames) {
      sanitizedMessage = this.preserveFileNames(sanitizedMessage, message);
    }

    // Preserve file extensions if requested
    if (options.preserveExtensions) {
      sanitizedMessage = this.preserveFileExtensions(sanitizedMessage, message);
    }

    // Add sanitized context if provided and requested
    if (context && options.includeContext) {
      const sanitizedContext = this.sanitizeContext(context);
      sanitizedMessage += ` Context: ${JSON.stringify(sanitizedContext)}`;
    }

    return sanitizedMessage;
  }

  /**
   * Create a secure error with sanitized message
   */
  static createSecureError(
    message: string,
    context?: Record<string, unknown>,
    originalError?: Error
  ): Error {
    const sanitizedMessage = this.sanitizeErrorMessage(message, context, {
      includeContext: true,
    });

    const error = new Error(sanitizedMessage);

    if (originalError) {
      error.cause = originalError;
      // Don't expose the original stack trace
      error.stack = error.stack;
    }

    return error;
  }

  /**
   * Preserve file names while sanitizing paths
   */
  private static preserveFileNames(
    sanitizedMessage: string,
    originalMessage: string
  ): string {
    const fileNamePattern = /\/([^\/\s]+\.[a-zA-Z0-9]+)/g;
    const matches = originalMessage.match(fileNamePattern);

    if (matches) {
      matches.forEach((match, index) => {
        const fileName = match.substring(1); // Remove leading slash
        sanitizedMessage = sanitizedMessage.replace(
          "[PATH_REDACTED]",
          `[PATH]/${fileName}`
        );
      });
    }

    return sanitizedMessage;
  }

  /**
   * Preserve file extensions for context
   */
  private static preserveFileExtensions(
    sanitizedMessage: string,
    originalMessage: string
  ): string {
    const extensionPattern = /\.([a-zA-Z0-9]+)(\s|$)/g;
    const matches = originalMessage.match(extensionPattern);

    if (matches) {
      const uniqueExtensions = [...new Set(matches)];
      uniqueExtensions.forEach((ext) => {
        if (!ext.match(/\.(key|pem|p12|mobileprovision)$/i)) {
          sanitizedMessage += ` [File type: ${ext.trim()}]`;
        }
      });
    }

    return sanitizedMessage;
  }

  /**
   * Enhanced context sanitization
   */
  private static sanitizeContext(
    context: Record<string, unknown>
  ): Record<string, unknown> {
    const sanitized: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(context)) {
      if (typeof value === "string") {
        sanitized[key] = this.sanitizeErrorMessage(value);
      } else if (typeof value === "object" && value !== null) {
        sanitized[key] = this.sanitizeContext(value as Record<string, unknown>);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Register a path mapping for consistent sanitization
   */
  static registerPathMapping(
    originalPath: string,
    sanitizedReference: string
  ): void {
    this.PATH_CONTEXT_MAP.set(originalPath, sanitizedReference);
  }

  /**
   * Get sanitized path reference
   */
  static getSanitizedPathReference(path: string): string {
    return this.PATH_CONTEXT_MAP.get(path) || "[PATH_REDACTED]";
  }
}

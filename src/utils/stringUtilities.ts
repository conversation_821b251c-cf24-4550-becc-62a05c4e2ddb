/**
 * String manipulation utilities
 * Extracted from common.ts for better organization
 */

/**
 * String manipulation utilities
 */
export class StringUtils {
  /**
   * Escape special characters in a string for use in a regular expression
   */
  static escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }

  /**
   * Truncate a string to a maximum length with ellipsis
   */
  static truncate(str: string, maxLength: number, suffix = "..."): string {
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength - suffix.length) + suffix;
  }

  /**
   * Convert a string to camelCase
   */
  static toCamelCase(str: string): string {
    return str.replace(/[-_\s]+(.)?/g, (_, char) =>
      char ? char.toUpperCase() : ""
    );
  }

  /**
   * Convert a string to kebab-case
   */
  static toKebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, "$1-$2")
      .replace(/[\s_]+/g, "-")
      .toLowerCase();
  }

  /**
   * Convert a string to snake_case
   */
  static toSnakeCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, "$1_$2")
      .replace(/[\s-]+/g, "_")
      .toLowerCase();
  }

  /**
   * Convert a string to PascalCase
   */
  static toPascalCase(str: string): string {
    const camelCase = this.toCamelCase(str);
    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
  }

  /**
   * Capitalize first letter of string
   */
  static capitalize(str: string): string {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  /**
   * Check if a string is empty or contains only whitespace
   */
  static isBlank(str: string | null | undefined): boolean {
    return !str || str.trim().length === 0;
  }

  /**
   * Remove ANSI color codes from a string
   */
  static stripAnsiCodes(str: string): string {
    return str.replace(/\x1b\[[0-9;]*m/g, "");
  }

  /**
   * Parse a version string into components
   */
  static parseVersion(version: string): {
    major: number;
    minor: number;
    patch: number;
    prerelease?: string;
  } {
    const match = version.match(/^(\d+)\.(\d+)\.(\d+)(?:-(.+))?$/);
    if (!match) {
      throw new Error(`Invalid version format: ${version}`);
    }

    return {
      major: parseInt(match[1], 10),
      minor: parseInt(match[2], 10),
      patch: parseInt(match[3], 10),
      prerelease: match[4],
    };
  }

  /**
   * Compare two version strings
   */
  static compareVersions(a: string, b: string): number {
    const versionA = StringUtils.parseVersion(a);
    const versionB = StringUtils.parseVersion(b);

    if (versionA.major !== versionB.major)
      return versionA.major - versionB.major;
    if (versionA.minor !== versionB.minor)
      return versionA.minor - versionB.minor;
    if (versionA.patch !== versionB.patch)
      return versionA.patch - versionB.patch;

    // Handle prerelease versions
    if (versionA.prerelease && !versionB.prerelease) return -1;
    if (!versionA.prerelease && versionB.prerelease) return 1;
    if (versionA.prerelease && versionB.prerelease) {
      return versionA.prerelease.localeCompare(versionB.prerelease);
    }

    return 0;
  }

  /**
   * Generate a random string of specified length
   */
  static randomString(length: number, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return result;
  }

  /**
   * Pad a string to a specified length
   */
  static pad(str: string, length: number, char = ' ', direction: 'left' | 'right' | 'both' = 'right'): string {
    if (str.length >= length) return str;
    
    const padLength = length - str.length;
    const padString = char.repeat(padLength);
    
    switch (direction) {
      case 'left':
        return padString + str;
      case 'both':
        const leftPad = Math.floor(padLength / 2);
        const rightPad = padLength - leftPad;
        return char.repeat(leftPad) + str + char.repeat(rightPad);
      case 'right':
      default:
        return str + padString;
    }
  }

  /**
   * Count occurrences of a substring in a string
   */
  static countOccurrences(str: string, substring: string): number {
    if (!substring) return 0;
    let count = 0;
    let position = 0;
    
    while ((position = str.indexOf(substring, position)) !== -1) {
      count++;
      position += substring.length;
    }
    
    return count;
  }

  /**
   * Replace all occurrences of a substring with another string
   */
  static replaceAll(str: string, search: string, replace: string): string {
    return str.split(search).join(replace);
  }

  /**
   * Extract words from a string
   */
  static extractWords(str: string): string[] {
    return str.match(/\b\w+\b/g) || [];
  }

  /**
   * Check if a string contains only alphanumeric characters
   */
  static isAlphanumeric(str: string): boolean {
    return /^[a-zA-Z0-9]+$/.test(str);
  }

  /**
   * Check if a string is a valid email address
   */
  static isEmail(str: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(str);
  }

  /**
   * Check if a string is a valid URL
   */
  static isUrl(str: string): boolean {
    try {
      new URL(str);
      return true;
    } catch {
      return false;
    }
  }

  // Backward compatibility aliases
  static camelCase = StringUtils.toCamelCase;
  static kebabCase = StringUtils.toKebabCase;
  static snakeCase = StringUtils.toSnakeCase;
}

/**
 * Array manipulation utilities
 */
export class ArrayUtils {
  /**
   * Remove duplicates from an array
   */
  static unique<T>(array: T[]): T[] {
    return [...new Set(array)];
  }

  /**
   * Remove duplicates from an array using a key function
   */
  static uniqueBy<T, K>(array: T[], keyFn: (item: T) => K): T[] {
    const seen = new Set<K>();
    return array.filter((item) => {
      const key = keyFn(item);
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  /**
   * Group array elements by a key function
   */
  static groupBy<T, K extends string | number | symbol>(
    array: T[],
    keyFn: (item: T) => K
  ): Record<K, T[]> {
    return array.reduce((groups, item) => {
      const key = keyFn(item);
      if (!groups[key]) groups[key] = [];
      groups[key].push(item);
      return groups;
    }, {} as Record<K, T[]>);
  }

  /**
   * Chunk an array into smaller arrays of specified size
   */
  static chunk<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Flatten a nested array
   */
  static flatten<T>(array: (T | T[])[]): T[] {
    return array.reduce<T[]>((flat, item) => {
      return flat.concat(Array.isArray(item) ? ArrayUtils.flatten(item) : item);
    }, []);
  }

  /**
   * Find the intersection of two arrays
   */
  static intersection<T>(a: T[], b: T[]): T[] {
    const setB = new Set(b);
    return a.filter((item) => setB.has(item));
  }

  /**
   * Find the difference between two arrays (items in a but not in b)
   */
  static difference<T>(a: T[], b: T[]): T[] {
    const setB = new Set(b);
    return a.filter((item) => !setB.has(item));
  }

  /**
   * Shuffle an array randomly
   */
  static shuffle<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Get a random element from an array
   */
  static randomElement<T>(array: T[]): T | undefined {
    if (array.length === 0) return undefined;
    return array[Math.floor(Math.random() * array.length)];
  }
}

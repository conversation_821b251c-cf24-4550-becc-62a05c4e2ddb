/**
 * Unified tool infrastructure
 * Combines functionality from toolBase.ts, toolFactory.ts, and toolHelpers.ts
 */

import { z } from "zod";
import * as fs from "fs/promises";
import * as path from "path";
import { XcodeServer } from "../server.js";
import {
  XcodeServerError,
  ValidationError,
  ProjectNotFoundError,
  PathAccessError,
  FileOperationError,
} from "./errors.js";
import { SecureMessageFormatter } from "./commandUtilities.js";
import { ValidationUtils } from "./validationUtilities.js";
import { globalPerformanceMonitor } from "./performanceMonitor.js";

/**
 * Tool handler function type
 */
export type ToolHandler<T = any> = (
  params: T,
  server: XcodeServer
) => Promise<ToolResult>;

/**
 * Tool result structure
 */
export interface ToolResult {
  content: Array<
    | {
        type: "text";
        text: string;
      }
    | {
        type: "image";
        data: string;
        mimeType: string;
      }
    | {
        type: "resource";
        resource: {
          uri: string;
          text?: string;
          mimeType?: string;
        };
      }
  >;
  isError?: boolean;
  _meta?: Record<string, unknown>;
}

/**
 * Tool registration options
 */
export interface ToolOptions {
  requiresProject?: boolean;
  validatePaths?: boolean;
  timeout?: number;
  retries?: number;
  cache?: {
    enabled: boolean;
    ttl?: number;
    keyGenerator?: (params: any) => string;
  };
}

/**
 * Tool metadata for documentation and validation
 */
export interface ToolMetadata {
  category: string;
  description: string;
  examples?: string[];
  seeAlso?: string[];
  version?: string;
  deprecated?: boolean;
  deprecationMessage?: string;
  securityLevel?: "low" | "medium" | "high";
  requiresValidation?: boolean;
}

/**
 * Tool categories for organization
 */
export enum ToolCategories {
  PROJECT = "project",
  FILE = "file",
  BUILD = "build",
  COCOAPODS = "cocoapods",
  SPM = "spm",
  SIMULATOR = "simulator",
  XCODE = "xcode",
  UTILITY = "utility",
}

/**
 * Common Zod schemas for reuse across tools
 */
export class CommonSchemas {
  static readonly filePath = z
    .string()
    .min(1, "File path cannot be empty")
    .refine(
      (path) => !path.includes("..") && !path.includes("\0"),
      "Invalid characters in file path"
    );

  static readonly safeFileName = z
    .string()
    .min(1, "File name cannot be empty")
    .refine(
      (name) => ValidationUtils.isSafeFileName(name),
      "File name contains invalid characters"
    );

  static readonly identifier = z
    .string()
    .min(1, "Identifier cannot be empty")
    .refine(
      (id) => ValidationUtils.isSafeIdentifier(id),
      "Identifier contains invalid characters"
    );

  static readonly optionalString = z.string().optional();
  static readonly optionalBoolean = z.boolean().optional();
  static readonly optionalNumber = z.number().optional();

  static readonly positiveNumber = z
    .number()
    .positive("Must be a positive number");
  static readonly nonNegativeNumber = z.number().min(0, "Must be non-negative");
}

/**
 * Base class for tool implementations providing common patterns
 */
export abstract class ToolBase<TParams = any> {
  protected server: XcodeServer;
  protected toolName: string;
  protected description: string;
  protected schema: z.ZodSchema<TParams>;

  constructor(
    server: XcodeServer,
    toolName: string,
    description: string,
    schema: z.ZodSchema<TParams>
  ) {
    this.server = server;
    this.toolName = toolName;
    this.description = description;
    this.schema = schema;
  }

  /**
   * Abstract method that must be implemented by subclasses
   */
  protected abstract executeImpl(params: TParams): Promise<ToolResult>;

  /**
   * Validate parameters using the schema
   */
  protected validateParams(params: unknown): TParams {
    try {
      return this.schema.parse(params);
    } catch (error) {
      throw new Error(
        `Parameter validation failed for ${this.toolName}: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Standard path validation and resolution
   */
  protected async validateAndResolvePath(
    inputPath: string,
    operation: "read" | "write" = "read"
  ): Promise<string> {
    // Expand tilde and environment variables
    const expandedPath = this.server.pathManager.expandPath(inputPath);

    // Resolve relative paths
    const resolvedPath = this.server.directoryState.resolvePath(expandedPath);

    // Validate path boundaries
    if (operation === "read") {
      return this.server.pathManager.validatePathForReading(resolvedPath);
    } else {
      return this.server.pathManager.validatePathForWriting(resolvedPath);
    }
  }

  /**
   * Standard command execution with error handling
   */
  protected async executeCommand(
    command: string,
    args: string[],
    options: { timeout?: number; cwd?: string } = {}
  ): Promise<{ stdout: string; stderr: string }> {
    try {
      return await this.server.commandExecutor.execute(command, args, {
        timeout: options.timeout || 30000,
        cwd: options.cwd,
      });
    } catch (error) {
      throw new Error(
        SecureMessageFormatter.formatError(
          `Command execution failed: ${command} ${args.join(" ")}`,
          {
            command,
            args,
            error: error instanceof Error ? error.message : String(error),
          }
        )
      );
    }
  }

  /**
   * Create a standardized success response
   */
  protected createSuccessResponse(message: string, data?: unknown): ToolResult {
    return {
      content: [
        {
          type: "text",
          text: SecureMessageFormatter.formatSuccess(message, data),
        },
      ],
    };
  }

  /**
   * Create a standardized error response
   */
  protected createErrorResponse(message: string, error?: Error): ToolResult {
    return {
      content: [
        {
          type: "text",
          text: SecureMessageFormatter.formatError(message, {
            error: error?.message,
            timestamp: new Date().toISOString(),
          }),
        },
      ],
      isError: true,
    };
  }

  /**
   * Execute the tool with standardized error handling and performance monitoring
   */
  async execute(params: unknown): Promise<ToolResult> {
    const startTime = Date.now();

    try {
      // Validate parameters
      const validatedParams = this.validateParams(params);

      // Execute the tool implementation
      const result = await this.executeImpl(validatedParams);

      // Record performance metrics
      globalPerformanceMonitor.recordMetric({
        name: this.toolName,
        duration: Date.now() - startTime,
        timestamp: new Date(),
        metadata: { success: true },
      });

      return result;
    } catch (error) {
      // Record failed operation
      globalPerformanceMonitor.recordMetric({
        name: this.toolName,
        duration: Date.now() - startTime,
        timestamp: new Date(),
        metadata: {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        },
      });

      // Return standardized error response
      return this.createErrorResponse(
        `Tool execution failed: ${this.toolName}`,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * Register this tool with the server
   */
  register(): void {
    this.server.server.tool(
      this.toolName,
      this.description,
      this.schema as any,
      this.execute.bind(this) as any
    );
  }
}

/**
 * Base class for file operation tools
 */
export abstract class FileToolBase<TParams = any> extends ToolBase<TParams> {
  /**
   * Check if a file exists
   */
  protected async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file stats with error handling
   */
  protected async getFileStats(filePath: string) {
    try {
      return await fs.stat(filePath);
    } catch (error) {
      throw new Error(
        `Cannot access file ${filePath}: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Ensure file exists with standardized error messaging
   */
  protected async ensureFileExists(
    filePath: string,
    fileType = "file"
  ): Promise<void> {
    try {
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        throw new FileOperationError(
          "validate",
          filePath,
          new Error(`Path is not a ${fileType}`)
        );
      }
    } catch (error) {
      if ((error as any).code === "ENOENT") {
        throw new FileOperationError(
          "access",
          filePath,
          new Error(`${fileType} not found`)
        );
      }
      throw error;
    }
  }

  /**
   * Validate file extension
   */
  protected validateFileExtension(
    filePath: string,
    expectedExtensions: string[]
  ): void {
    const ext = path.extname(filePath).toLowerCase();
    if (!expectedExtensions.includes(ext)) {
      throw new Error(
        `File must have one of these extensions: ${expectedExtensions.join(
          ", "
        )}`
      );
    }
  }
}

/**
 * Base class for project operation tools
 */
export abstract class ProjectToolBase<TParams = any> extends ToolBase<TParams> {
  /**
   * Ensure active project exists
   */
  protected ensureActiveProject(): void {
    if (!this.server.activeProject) {
      throw new Error(
        "No active project set. Use set_project_path to set an active project first."
      );
    }
  }

  /**
   * Get active project directory
   */
  protected getActiveProjectDirectory(): string {
    this.ensureActiveProject();
    return this.server.directoryState.getActiveDirectory();
  }

  /**
   * Validate project-related path
   */
  protected async validateProjectPath(inputPath: string): Promise<string> {
    const resolvedPath = await this.validateAndResolvePath(inputPath, "read");

    // Ensure path is within project boundaries
    const projectDir = this.getActiveProjectDirectory();
    if (!resolvedPath.startsWith(projectDir)) {
      throw new Error(
        `Path ${resolvedPath} is outside the active project directory`
      );
    }

    return resolvedPath;
  }

  /**
   * Get project info with caching and error handling
   */
  protected async getProjectInfoSafely(): Promise<any> {
    this.ensureActiveProject();

    const cacheKey = `project_info_${this.server.activeProject!.path}`;
    return this.server.cache.getOrSet(
      cacheKey,
      async () => {
        const { getProjectInfo, getWorkspaceInfo } = await import(
          "./projectManager.js"
        );

        if (this.server.activeProject!.isWorkspace) {
          return getWorkspaceInfo(
            this.server.activeProject!.path,
            this.server.commandExecutor
          );
        } else {
          return getProjectInfo(
            this.server.activeProject!.path,
            this.server.commandExecutor
          );
        }
      },
      { ttl: 300000 } // 5 minutes TTL
    );
  }

  /**
   * Validate scheme exists in project
   */
  protected async validateScheme(scheme: string): Promise<void> {
    const projectInfo = await this.getProjectInfoSafely();
    if (!projectInfo.schemes.includes(scheme)) {
      throw new XcodeServerError(
        `Invalid scheme "${scheme}". Available schemes: ${projectInfo.schemes.join(
          ", "
        )}`
      );
    }
  }

  /**
   * Validate configuration exists in project
   */
  protected async validateConfiguration(configuration: string): Promise<void> {
    const projectInfo = await this.getProjectInfoSafely();
    if (!projectInfo.configurations.includes(configuration)) {
      throw new XcodeServerError(
        `Invalid configuration "${configuration}". Available configurations: ${projectInfo.configurations.join(
          ", "
        )}`
      );
    }
  }
}

/**
 * Base class for command-line tool wrappers
 */
export abstract class CommandToolBase<TParams = any> extends ToolBase<TParams> {
  protected abstract getCommand(): string;
  protected abstract buildArgs(params: TParams): string[];

  /**
   * Execute the command with standardized handling
   */
  protected async executeToolCommand(
    params: TParams,
    options: { timeout?: number; cwd?: string } = {}
  ): Promise<{ stdout: string; stderr: string }> {
    const command = this.getCommand();
    const args = this.buildArgs(params);

    return this.executeCommand(command, args, options);
  }

  /**
   * Execute command and return formatted result
   */
  protected async executeAndFormatResult(
    params: TParams,
    successMessage: string,
    options: { timeout?: number; cwd?: string } = {}
  ): Promise<ToolResult> {
    const result = await this.executeToolCommand(params, options);

    const formattedOutput = SecureMessageFormatter.formatCommandOutput(
      result.stdout,
      result.stderr
    );

    return {
      content: [
        {
          type: "text",
          text: SecureMessageFormatter.formatSuccess(
            successMessage,
            formattedOutput
          ),
        },
      ],
    };
  }
}

/**
 * Factory for creating standardized tool registrations
 */
export class ToolFactory {
  /**
   * Register a tool with standardized error handling and validation
   */
  static registerTool<T>(
    server: XcodeServer,
    name: string,
    description: string,
    schema: z.ZodSchema<T>,
    handler: ToolHandler<T>,
    options: ToolOptions = {},
    metadata?: ToolMetadata
  ): void {
    // Enhanced wrapper with security, performance monitoring, and error handling
    const wrappedHandler = this.wrapHandler(handler, options, metadata);

    server.server.tool(name, description, schema as any, wrappedHandler as any);
  }

  /**
   * Create a standardized success result
   */
  static createSuccessResult(message: string, data?: unknown): ToolResult {
    return {
      content: [
        {
          type: "text",
          text: SecureMessageFormatter.formatSuccess(message, data),
        },
      ],
    };
  }

  /**
   * Create a standardized error result
   */
  static createErrorResult(
    error: Error | string,
    context?: Record<string, unknown>
  ): ToolResult {
    const message = error instanceof Error ? error.message : error;
    return {
      content: [
        {
          type: "text",
          text: SecureMessageFormatter.formatError(message, context),
        },
      ],
      isError: true,
    };
  }

  /**
   * Register a project-dependent tool
   */
  static registerProjectTool<T>(
    server: XcodeServer,
    name: string,
    description: string,
    schema: z.ZodSchema<T>,
    handler: ToolHandler<T>,
    options: Omit<ToolOptions, "requiresProject"> = {},
    metadata?: ToolMetadata
  ): void {
    ToolFactory.registerTool(
      server,
      name,
      description,
      schema,
      handler,
      { ...options, requiresProject: true },
      metadata
    );
  }

  /**
   * Register a file operation tool
   */
  static registerFileTool<T>(
    server: XcodeServer,
    name: string,
    description: string,
    schema: z.ZodSchema<T>,
    handler: ToolHandler<T>,
    options: Omit<ToolOptions, "validatePaths"> = {},
    metadata?: ToolMetadata
  ): void {
    ToolFactory.registerTool(
      server,
      name,
      description,
      schema,
      handler,
      { ...options, validatePaths: true },
      metadata
    );
  }

  /**
   * Register a cached tool
   */
  static registerCachedTool<T>(
    server: XcodeServer,
    name: string,
    description: string,
    schema: z.ZodSchema<T>,
    handler: ToolHandler<T>,
    cacheOptions: NonNullable<ToolOptions["cache"]>,
    options: Omit<ToolOptions, "cache"> = {},
    metadata?: ToolMetadata
  ): void {
    ToolFactory.registerTool(
      server,
      name,
      description,
      schema,
      handler,
      { ...options, cache: { ...cacheOptions, enabled: true } },
      metadata
    );
  }

  /**
   * Wrap a tool handler with standardized functionality
   */
  private static wrapHandler<T>(
    handler: ToolHandler<T>,
    options: ToolOptions,
    metadata?: ToolMetadata
  ): (params: T, extra: any) => Promise<ToolResult> {
    return async (params: T, extra: any): Promise<ToolResult> => {
      const toolName = metadata?.category
        ? `${metadata.category}_tool`
        : "unknown_tool";
      const timer = globalPerformanceMonitor.startTimer(toolName);

      try {
        // Security validation
        if (metadata?.securityLevel === "high" || options.validatePaths) {
          this.validateSecurityRequirements(params, options);
        }

        // Check deprecation
        if (metadata?.deprecated) {
          console.warn(
            SecureMessageFormatter.formatError(
              `Tool is deprecated: ${
                metadata.deprecationMessage || "Please use an alternative"
              }`
            )
          );
        }

        // Execute with timeout if specified
        let result: ToolResult;
        if (options.timeout) {
          result = await this.executeWithTimeout(
            () => handler(params, extra.server || extra),
            options.timeout
          );
        } else {
          result = await handler(params, extra.server || extra);
        }

        timer.stop({ success: true });
        return result;
      } catch (error) {
        const duration = timer.stop({
          success: false,
          error: error instanceof Error ? error.message : String(error),
        });

        // Log slow operations
        if (duration > 5000) {
          console.warn(
            SecureMessageFormatter.formatError(
              `Slow tool execution: ${duration}ms`
            )
          );
        }

        return this.createErrorResult(
          error instanceof Error ? error : new Error(String(error))
        );
      }
    };
  }

  /**
   * Validate security requirements for tool parameters
   */
  private static validateSecurityRequirements<T>(
    params: T,
    options: ToolOptions
  ): void {
    if (typeof params === "object" && params !== null) {
      const paramObj = params as Record<string, unknown>;

      // Validate file paths
      if (options.validatePaths) {
        for (const [key, value] of Object.entries(paramObj)) {
          if (
            typeof value === "string" &&
            (key.includes("path") || key.includes("Path"))
          ) {
            if (value.includes("..") || value.includes("\0")) {
              throw new ValidationError(
                key,
                value,
                "Invalid characters in path"
              );
            }
          }
        }
      }
    }
  }

  /**
   * Execute a function with timeout
   */
  private static async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(
        () => reject(new Error(`Operation timed out after ${timeoutMs}ms`)),
        timeoutMs
      );

      operation()
        .then((result) => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }
}

/**
 * Common validation utilities for tools
 */
export class ToolValidationHelpers {
  /**
   * Ensure active project exists, throw standardized error if not
   */
  static ensureActiveProject(server: XcodeServer): void {
    if (!server.activeProject) {
      throw new ProjectNotFoundError(
        "No active Xcode project detected. Use set_project_path or detect_active_project first."
      );
    }
  }

  /**
   * Check if a directory exists with standardized error messaging
   */
  static async ensureDirectoryExists(
    dirPath: string,
    dirType = "directory"
  ): Promise<void> {
    try {
      const stats = await fs.stat(dirPath);
      if (!stats.isDirectory()) {
        throw new FileOperationError(
          "validate",
          dirPath,
          new Error(`Path is not a ${dirType}`)
        );
      }
    } catch (error) {
      if ((error as any).code === "ENOENT") {
        throw new FileOperationError(
          "access",
          dirPath,
          new Error(`${dirType} not found`)
        );
      }
      throw error;
    }
  }

  /**
   * Validate app bundle (.app directory)
   */
  static async validateAppBundle(appPath: string): Promise<void> {
    if (!appPath.endsWith(".app")) {
      throw new Error("Path must end with .app extension");
    }

    try {
      const stats = await fs.stat(appPath);
      if (!stats.isDirectory()) {
        throw new Error("App bundle must be a directory");
      }
    } catch (error) {
      if ((error as any).code === "ENOENT") {
        throw new Error("App bundle not found");
      }
      throw error;
    }
  }

  /**
   * Check for required files in a directory (e.g., Podfile, Package.swift)
   */
  static async checkRequiredFiles(
    directory: string,
    requiredFiles: string[],
    projectType: string
  ): Promise<void> {
    const missingFiles: string[] = [];

    for (const file of requiredFiles) {
      const filePath = path.join(directory, file);
      try {
        await fs.access(filePath);
      } catch {
        missingFiles.push(file);
      }
    }

    if (missingFiles.length > 0) {
      throw new XcodeServerError(
        `Missing required ${projectType} files: ${missingFiles.join(", ")}. ` +
          `This project doesn't appear to use ${projectType}.`
      );
    }
  }
}

// Backward compatibility exports
export { ToolBase as ToolBaseClass };
export { FileToolBase as FileToolBaseClass };
export { ProjectToolBase as ProjectToolBaseClass };
export { CommandToolBase as CommandToolBaseClass };

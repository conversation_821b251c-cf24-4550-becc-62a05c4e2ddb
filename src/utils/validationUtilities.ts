/**
 * Data validation utilities
 * Extracted from common.ts and enhanced for better organization
 */

/**
 * Object manipulation utilities
 */
export class ObjectUtils {
  /**
   * Deep clone an object
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== "object") return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
    if (obj instanceof Array)
      return obj.map((item) => ObjectUtils.deepClone(item)) as unknown as T;
    if (typeof obj === "object") {
      const cloned = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = ObjectUtils.deepClone(obj[key]);
        }
      }
      return cloned;
    }
    return obj;
  }

  /**
   * Deep merge two objects
   */
  static deepMerge<T extends Record<string, any>>(
    target: T,
    source: Partial<T>
  ): T {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key];
        const targetValue = result[key];

        if (
          ObjectUtils.isObject(sourceValue) &&
          ObjectUtils.isObject(targetValue)
        ) {
          result[key] = ObjectUtils.deepMerge(targetValue, sourceValue as any);
        } else {
          result[key] = sourceValue as T[Extract<keyof T, string>];
        }
      }
    }

    return result;
  }

  /**
   * Check if a value is a plain object
   */
  static isObject(value: any): value is Record<string, any> {
    return value !== null && typeof value === "object" && !Array.isArray(value);
  }

  /**
   * Get a nested property value using dot notation
   */
  static get(obj: any, path: string, defaultValue?: any): any {
    const keys = path.split(".");
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined || !(key in current)) {
        return defaultValue;
      }
      current = current[key];
    }

    return current;
  }

  /**
   * Set a nested property value using dot notation
   */
  static set(obj: any, path: string, value: any): void {
    const keys = path.split(".");
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || !ObjectUtils.isObject(current[key])) {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
  }

  /**
   * Pick specific properties from an object
   */
  static pick<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[]
  ): Pick<T, K> {
    const result = {} as Pick<T, K>;
    for (const key of keys) {
      if (key in obj) {
        result[key] = obj[key];
      }
    }
    return result;
  }

  /**
   * Omit specific properties from an object
   */
  static omit<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[]
  ): Omit<T, K> {
    const result = { ...obj };
    for (const key of keys) {
      delete result[key];
    }
    return result;
  }

  /**
   * Check if an object has a specific property
   */
  static hasProperty(obj: any, property: string): boolean {
    return (
      obj !== null &&
      obj !== undefined &&
      Object.prototype.hasOwnProperty.call(obj, property)
    );
  }

  /**
   * Get all keys of an object including nested keys
   */
  static getAllKeys(obj: any, prefix = ""): string[] {
    const keys: string[] = [];

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        keys.push(fullKey);

        if (ObjectUtils.isObject(obj[key])) {
          keys.push(...ObjectUtils.getAllKeys(obj[key], fullKey));
        }
      }
    }

    return keys;
  }

  /**
   * Check if two objects are deeply equal
   */
  static deepEqual(a: any, b: any): boolean {
    if (a === b) return true;

    if (a === null || b === null) return false;
    if (typeof a !== typeof b) return false;

    if (typeof a !== "object") return false;

    if (Array.isArray(a) !== Array.isArray(b)) return false;

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);

    if (keysA.length !== keysB.length) return false;

    for (const key of keysA) {
      if (!keysB.includes(key)) return false;
      if (!ObjectUtils.deepEqual(a[key], b[key])) return false;
    }

    return true;
  }
}

/**
 * Validation utilities for data integrity and type checking
 */
export class ValidationUtils {
  /**
   * Check if a value is null or undefined
   */
  static isNullOrUndefined(value: any): value is null | undefined {
    return value === null || value === undefined;
  }

  /**
   * Check if a value is a string
   */
  static isString(value: any): value is string {
    return typeof value === "string";
  }

  /**
   * Check if a value is a number
   */
  static isNumber(value: any): value is number {
    return typeof value === "number" && !isNaN(value);
  }

  /**
   * Check if a value is a boolean
   */
  static isBoolean(value: any): value is boolean {
    return typeof value === "boolean";
  }

  /**
   * Check if a value is an array
   */
  static isArray(value: any): value is any[] {
    return Array.isArray(value);
  }

  /**
   * Check if a value is a function
   */
  static isFunction(value: any): value is Function {
    return typeof value === "function";
  }

  /**
   * Check if a value is a Date object
   */
  static isDate(value: any): value is Date {
    return value instanceof Date && !isNaN(value.getTime());
  }

  /**
   * Check if a value is a valid integer
   */
  static isInteger(value: any): value is number {
    return Number.isInteger(value);
  }

  /**
   * Check if a value is a positive number
   */
  static isPositive(value: any): value is number {
    return ValidationUtils.isNumber(value) && value > 0;
  }

  /**
   * Check if a value is a non-negative number
   */
  static isNonNegative(value: any): value is number {
    return ValidationUtils.isNumber(value) && value >= 0;
  }

  /**
   * Check if a string is empty or whitespace only
   */
  static isEmpty(value: string): boolean {
    return !value || value.trim().length === 0;
  }

  /**
   * Check if an array is empty
   */
  static isEmptyArray(value: any[]): boolean {
    return !value || value.length === 0;
  }

  /**
   * Check if an object is empty
   */
  static isEmptyObject(value: object): boolean {
    return !value || Object.keys(value).length === 0;
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate URL format
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate that a value is within a range
   */
  static isInRange(value: number, min: number, max: number): boolean {
    return ValidationUtils.isNumber(value) && value >= min && value <= max;
  }

  /**
   * Validate that a string matches a pattern
   */
  static matchesPattern(value: string, pattern: RegExp): boolean {
    return ValidationUtils.isString(value) && pattern.test(value);
  }

  /**
   * Validate that a value is one of the allowed values
   */
  static isOneOf<T>(value: any, allowedValues: T[]): value is T {
    return allowedValues.includes(value);
  }

  /**
   * Validate that an array contains only unique values
   */
  static hasUniqueValues<T>(array: T[]): boolean {
    if (!ValidationUtils.isArray(array)) return false;
    return array.length === new Set(array).size;
  }

  /**
   * Validate that an object has required properties
   */
  static hasRequiredProperties(obj: any, requiredProps: string[]): boolean {
    if (!ObjectUtils.isObject(obj)) return false;
    return requiredProps.every((prop) => ObjectUtils.hasProperty(obj, prop));
  }

  /**
   * Validate JSON string
   */
  static isValidJson(jsonString: string): boolean {
    try {
      JSON.parse(jsonString);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate that a value is a valid port number
   */
  static isValidPort(port: any): port is number {
    return (
      ValidationUtils.isInteger(port) &&
      ValidationUtils.isInRange(port, 1, 65535)
    );
  }

  /**
   * Validate that a string is a valid IPv4 address
   */
  static isValidIPv4(ip: string): boolean {
    const ipv4Regex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipv4Regex.test(ip);
  }

  /**
   * Validate that a string is a valid semantic version
   */
  static isValidSemVer(version: string): boolean {
    const semVerRegex =
      /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;
    return semVerRegex.test(version);
  }

  /**
   * Validate that a string contains only safe characters for file names
   */
  static isSafeFileName(fileName: string): boolean {
    // Allow alphanumeric, dots, hyphens, underscores, and spaces
    const safeFileNameRegex = /^[a-zA-Z0-9._\-\s]+$/;
    return safeFileNameRegex.test(fileName) && !fileName.includes("..");
  }

  /**
   * Validate that a string is a safe identifier (for schemes, targets, etc.)
   */
  static isSafeIdentifier(identifier: string): boolean {
    // Allow alphanumeric, hyphens, and underscores only
    const safeIdentifierRegex = /^[a-zA-Z0-9_\-]+$/;
    return safeIdentifierRegex.test(identifier);
  }

  /**
   * Check if a string is a valid UUID
   */
  static isUuid(uuid: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Sanitize a string for safe logging (removes potential sensitive data)
   */
  static sanitizeForLogging(input: string): string {
    return input
      .replace(/\/Users\/<USER>\/\s]+/g, "/Users/<USER>") // Hide usernames in paths
      .replace(/\/home\/<USER>\/\s]+/g, "/home/<USER>") // Hide usernames in Linux paths
      .replace(/[A-Za-z]:\\Users\\[^\\]+/g, "C:\\Users\\<USER>\s*[^\s]+/gi, "password=***") // Hide passwords
      .replace(/token[=:]\s*[^\s]+/gi, "token=***") // Hide tokens
      .replace(/key[=:]\s*[^\s]+/gi, "key=***"); // Hide keys
  }
}

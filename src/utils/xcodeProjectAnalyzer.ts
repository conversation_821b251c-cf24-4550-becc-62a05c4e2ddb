/**
 * Xcode Project Analysis Service
 * Provides comprehensive project analysis, code intelligence, and architectural insights
 * for enhanced AI agent development and project understanding
 */

import * as fs from "fs/promises";
import * as path from "path";
import { XcodeProject, ProjectInfo } from "../types/index.js";
import { PathManager } from "./pathManager.js";
import { UnifiedCache } from "./cacheManager.js";
import { SecureErrorFormatter } from "./securityManager.js";

/**
 * Project structure analysis result
 */
export interface ProjectAnalysis {
  structure: {
    totalFiles: number;
    swiftFiles: number;
    swiftUIFiles: number;
    testFiles: number;
    resourceFiles: number;
  };
  dependencies: {
    cocoapods: string[];
    spm: string[];
    frameworks: string[];
  };
  architecture: {
    patterns: string[];
    complexity: "low" | "medium" | "high";
    recommendations: string[];
  };
  codeQuality: {
    score: number;
    issues: Array<{
      type: string;
      severity: "low" | "medium" | "high";
      description: string;
      file?: string;
    }>;
  };
}

/**
 * Code intelligence features
 */
export interface CodeIntelligence {
  symbols: Array<{
    name: string;
    type: "class" | "struct" | "enum" | "protocol" | "function" | "variable";
    file: string;
    line: number;
    accessibility: "public" | "internal" | "private" | "fileprivate";
  }>;
  dependencies: Array<{
    from: string;
    to: string;
    type: "import" | "inheritance" | "composition" | "protocol";
  }>;
  suggestions: Array<{
    type: "refactor" | "optimize" | "modernize" | "security";
    description: string;
    file: string;
    priority: "low" | "medium" | "high";
  }>;
}

/**
 * Xcode Project Analysis Service
 * Provides comprehensive project analysis and code intelligence for AI development
 */
export class XcodeProjectAnalyzer {
  private static readonly analysisCache = new UnifiedCache<ProjectAnalysis>({
    defaultTtl: 600000, // 10 minutes
    maxSize: 50,
  });

  private static readonly codeIntelligenceCache =
    new UnifiedCache<CodeIntelligence>({
      defaultTtl: 300000, // 5 minutes
      maxSize: 100,
    });

  /**
   * Perform comprehensive project analysis
   */
  static async analyzeProject(
    projectPath: string,
    pathManager: PathManager
  ): Promise<ProjectAnalysis> {
    const cacheKey = `analysis_${projectPath}`;

    return this.analysisCache.getOrSet(cacheKey, async () => {
      try {
        const structure = await this.analyzeProjectStructure(
          projectPath,
          pathManager
        );
        const dependencies = await this.analyzeDependencies(
          projectPath,
          pathManager
        );
        const architecture = await this.analyzeArchitecture(
          projectPath,
          pathManager
        );
        const codeQuality = await this.analyzeCodeQuality(
          projectPath,
          pathManager
        );

        return {
          structure,
          dependencies,
          architecture,
          codeQuality,
        };
      } catch (error) {
        throw SecureErrorFormatter.createSecureError(
          "Failed to analyze project",
          { projectPath },
          error instanceof Error ? error : undefined
        );
      }
    });
  }

  /**
   * Analyze project file structure
   */
  private static async analyzeProjectStructure(
    projectPath: string,
    pathManager: PathManager
  ): Promise<ProjectAnalysis["structure"]> {
    const files = await this.getAllProjectFiles(projectPath, pathManager);

    const structure = {
      totalFiles: files.length,
      swiftFiles: files.filter((f) => f.endsWith(".swift")).length,
      swiftUIFiles: 0,
      testFiles: files.filter((f) => f.includes("Test") || f.includes("test"))
        .length,
      resourceFiles: files.filter(
        (f) =>
          f.endsWith(".xcassets") ||
          f.endsWith(".storyboard") ||
          f.endsWith(".xib") ||
          f.endsWith(".plist")
      ).length,
    };

    // Analyze SwiftUI usage
    for (const file of files.filter((f) => f.endsWith(".swift"))) {
      try {
        const content = await fs.readFile(file, "utf-8");
        if (
          content.includes("import SwiftUI") ||
          content.includes("View") ||
          content.includes("@State")
        ) {
          structure.swiftUIFiles++;
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }

    return structure;
  }

  /**
   * Analyze project dependencies
   */
  private static async analyzeDependencies(
    projectPath: string,
    pathManager: PathManager
  ): Promise<ProjectAnalysis["dependencies"]> {
    const dependencies = {
      cocoapods: [] as string[],
      spm: [] as string[],
      frameworks: [] as string[],
    };

    try {
      // Analyze CocoaPods dependencies
      const podfilePath = path.join(projectPath, "Podfile");
      if (await this.fileExists(podfilePath)) {
        const podfileContent = await fs.readFile(podfilePath, "utf-8");
        const podMatches = podfileContent.match(/pod\s+['"]([^'"]+)['"]/g);
        if (podMatches) {
          dependencies.cocoapods = podMatches.map((match) =>
            match.replace(/pod\s+['"]([^'"]+)['"].*/, "$1")
          );
        }
      }

      // Analyze SPM dependencies
      const packagePath = path.join(projectPath, "Package.swift");
      if (await this.fileExists(packagePath)) {
        const packageContent = await fs.readFile(packagePath, "utf-8");
        const urlMatches = packageContent.match(/url:\s*["']([^"']+)["']/g);
        if (urlMatches) {
          dependencies.spm = urlMatches.map((match) =>
            match.replace(/url:\s*["']([^"']+)["'].*/, "$1")
          );
        }
      }

      // Analyze framework imports
      const swiftFiles = await this.getAllProjectFiles(
        projectPath,
        pathManager,
        ".swift"
      );
      const frameworkSet = new Set<string>();

      for (const file of swiftFiles.slice(0, 20)) {
        // Limit to first 20 files for performance
        try {
          const content = await fs.readFile(file, "utf-8");
          const importMatches = content.match(/import\s+(\w+)/g);
          if (importMatches) {
            importMatches.forEach((match) => {
              const framework = match.replace(/import\s+/, "");
              if (!["Foundation", "UIKit", "SwiftUI"].includes(framework)) {
                frameworkSet.add(framework);
              }
            });
          }
        } catch (error) {
          continue;
        }
      }

      dependencies.frameworks = Array.from(frameworkSet);
    } catch (error) {
      // Return partial results on error
    }

    return dependencies;
  }

  /**
   * Analyze project architecture patterns
   */
  private static async analyzeArchitecture(
    projectPath: string,
    pathManager: PathManager
  ): Promise<ProjectAnalysis["architecture"]> {
    const patterns: string[] = [];
    const recommendations: string[] = [];
    let complexity: "low" | "medium" | "high" = "low";

    try {
      const swiftFiles = await this.getAllProjectFiles(
        projectPath,
        pathManager,
        ".swift"
      );

      // Analyze architectural patterns
      let mvvmCount = 0;
      let mvcCount = 0;
      let coordinatorCount = 0;

      for (const file of swiftFiles.slice(0, 30)) {
        // Limit for performance
        try {
          const content = await fs.readFile(file, "utf-8");
          const fileName = path.basename(file);

          if (
            fileName.includes("ViewModel") ||
            content.includes("ObservableObject")
          ) {
            mvvmCount++;
          }
          if (
            fileName.includes("Controller") ||
            content.includes("UIViewController")
          ) {
            mvcCount++;
          }
          if (
            fileName.includes("Coordinator") ||
            content.includes("Coordinator")
          ) {
            coordinatorCount++;
          }
        } catch (error) {
          continue;
        }
      }

      if (mvvmCount > 0) patterns.push("MVVM");
      if (mvcCount > 0) patterns.push("MVC");
      if (coordinatorCount > 0) patterns.push("Coordinator");

      // Determine complexity
      const totalFiles = swiftFiles.length;
      if (totalFiles > 100) {
        complexity = "high";
        recommendations.push(
          "Consider modularizing the project into smaller components"
        );
      } else if (totalFiles > 30) {
        complexity = "medium";
        recommendations.push(
          "Monitor project growth and consider architectural improvements"
        );
      }

      // Architecture recommendations
      if (patterns.length === 0) {
        recommendations.push(
          "Consider adopting a clear architectural pattern (MVVM, MVC, etc.)"
        );
      }
      if (mvvmCount > 0 && mvcCount > 0) {
        recommendations.push(
          "Mixed architectural patterns detected - consider standardizing"
        );
      }
    } catch (error) {
      recommendations.push(
        "Unable to fully analyze architecture - manual review recommended"
      );
    }

    return {
      patterns,
      complexity,
      recommendations,
    };
  }

  /**
   * Analyze code quality
   */
  private static async analyzeCodeQuality(
    projectPath: string,
    pathManager: PathManager
  ): Promise<ProjectAnalysis["codeQuality"]> {
    const issues: ProjectAnalysis["codeQuality"]["issues"] = [];
    let score = 100;

    try {
      const swiftFiles = await this.getAllProjectFiles(
        projectPath,
        pathManager,
        ".swift"
      );

      for (const file of swiftFiles.slice(0, 20)) {
        // Limit for performance
        try {
          const content = await fs.readFile(file, "utf-8");
          const lines = content.split("\n");

          // Check for common issues
          if (lines.length > 500) {
            issues.push({
              type: "file_length",
              severity: "medium",
              description:
                "File is very long - consider breaking into smaller files",
              file: path.basename(file),
            });
            score -= 5;
          }

          // Check for force unwrapping
          const forceUnwrapCount = (content.match(/!/g) || []).length;
          if (forceUnwrapCount > 5) {
            issues.push({
              type: "force_unwrap",
              severity: "high",
              description:
                "Excessive force unwrapping detected - consider safer alternatives",
              file: path.basename(file),
            });
            score -= 10;
          }

          // Check for TODO/FIXME comments
          const todoCount = (content.match(/TODO|FIXME/gi) || []).length;
          if (todoCount > 0) {
            issues.push({
              type: "todo_comments",
              severity: "low",
              description: `${todoCount} TODO/FIXME comments found`,
              file: path.basename(file),
            });
            score -= 2;
          }
        } catch (error) {
          continue;
        }
      }
    } catch (error) {
      issues.push({
        type: "analysis_error",
        severity: "medium",
        description: "Unable to complete code quality analysis",
      });
      score -= 20;
    }

    return {
      score: Math.max(0, score),
      issues,
    };
  }

  /**
   * Get all project files with optional extension filter
   */
  private static async getAllProjectFiles(
    projectPath: string,
    pathManager: PathManager,
    extension?: string
  ): Promise<string[]> {
    const files: string[] = [];

    const scanDirectory = async (dir: string): Promise<void> => {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory()) {
            // Skip certain directories
            if (
              !["node_modules", ".git", "build", "DerivedData"].includes(
                entry.name
              )
            ) {
              await scanDirectory(fullPath);
            }
          } else if (entry.isFile()) {
            if (!extension || fullPath.endsWith(extension)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        // Skip directories that can't be read
      }
    };

    await scanDirectory(projectPath);
    return files;
  }

  /**
   * Check if file exists
   */
  private static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Clear analysis cache
   */
  static clearCache(): void {
    this.analysisCache.clear();
    this.codeIntelligenceCache.clear();
  }

  /**
   * Get cache statistics
   */
  static getCacheStats() {
    return {
      analysis: this.analysisCache.getStats(),
      codeIntelligence: this.codeIntelligenceCache.getStats(),
    };
  }
}
